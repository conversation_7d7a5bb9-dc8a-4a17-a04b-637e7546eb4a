<template>
  <view class="p-[24rpx_30rpx_0]" @tap="handleToPreviewDetail">
    <view class="flex flex-col gap-12rpx">
      <view class="flex items-center gap-26rpx">
        <wd-skeleton
          :loading="skeletonLoading"
          :row-col="[[{ width: '82rpx', height: '82rpx', type: 'circle' }]]"
        >
          <view
            :class="
              hxUserInfo?.isOnline || personalList?.ActivityStatus === '在线'
                ? 'border-twinkle bg_left_icon_box'
                : ''
            "
          >
            <wd-img :src="hxUserInfo?.avatar || avatarOne" height="82rpx" round width="82rpx" />
          </view>
        </wd-skeleton>
        <view class="flex flex-col gap-6rpx flex-1">
          <view class="c-#333333 flex items-center">
            <view class="flex-1 flex items-center">
              <text class="text-38rpx font-500">
                {{ hxUserInfo?.nickname }}
              </text>
              <!-- <view class="flex items-center" v-if="hxUserInfo?.isOnline">
                  <view class="mx-8rpx size-6rpx bg-#34A715 rounded-full" />
                  <text class="c-#34A715 text-24rpx">在线</text>
                </view> -->
              <view
                v-if="personalList?.ActivityStatus"
                :class="personalList?.ActivityStatus === '在线' ? 'c-#0ea500' : 'c-#666'"
                class="flex items-center text-24rpx"
              >
                <view
                  :class="personalList?.ActivityStatus === '在线' ? 'bg-#0ea500' : 'bg-#666'"
                  class="mx-8rpx size-6rpx rounded-full"
                />
                <text>{{ personalList?.ActivityStatus }}</text>
              </view>
            </view>
            <text class="c-#FF8080 text-32rpx font500">
              {{
                [
                  formatToKilo(personalList.salaryExpectationStart),
                  formatToKilo(personalList.salaryExpectationEnd),
                ]
                  .filter(Boolean)
                  .join('-') || '面议'
              }}
            </text>
          </view>
          <text class="c-#333333 text-24rpx line-clamp-1">
            {{
              [personOtherInfo.seekStatus, personOtherInfo.qualification, personalList.major]
                .filter(Boolean)
                .join(' | ')
            }}
          </text>
        </view>
      </view>
      <view
        v-if="personalList?.workExperienceList?.length"
        class="flex flex-col gap-12rpx relative"
      >
        <view
          v-for="(item, key) in (isWorkExperienceExpanded
            ? personalList?.workExperienceList
            : personalList?.workExperienceList?.slice(0, 1)) ?? []"
          :key="`workExperienceList-${key}`"
          class="flex items-center gap-16rpx relative"
          @tap.stop="
            !key && personalList?.workExperienceList?.length > 1
              ? toggleWorkExperience()
              : undefined
          "
        >
          <wd-icon v-if="key === 0" :name="postMark" size="26rpx" />
          <view v-else class="dot-separator" />

          <view class="flex items-center flex-1 line-clamp-1">
            <text class="c-#333333 text-24rpx">{{ item?.workCompanyName }}</text>
            <view
              v-if="item?.workCompanyName && (item?.workYears || !(item?.workYears ?? 0))"
              class="dot-separator"
            />
            <text class="c-#333333 text-24rpx">
              {{ !(item?.workYears ?? 0) ? '1年内' : `${item?.workYears}年以上` }}
            </text>
          </view>
          <text
            v-if="!key && personalList?.workExperienceList?.length > 1"
            :class="
              isWorkExperienceExpanded ? 'i-carbon-triangle-solid' : 'i-carbon-triangle-down-solid'
            "
            class="c-#000000 text-16rpx"
          />
        </view>
        <!-- 虚线连接器 -->
        <view
          v-if="isWorkExperienceExpanded && personalList?.workExperienceList?.length > 1"
          class="absolute left-13rpx top-40rpx bottom-28rpx w-0 border-l-2 border-l-dashed border-l-#d8d8d8"
          style="z-index: 0"
        />
      </view>
    </view>
    <view class="flex flex-col gap-12rpx mt-12rpx mb-12rpx">
      <text v-if="personalList?.myLights" class="c-#333333 text-24rpx line-clamp-1">
        {{ personalList.myLights }}
      </text>
      <view class="flex items-center flex-wrap gap-20rpx">
        <view
          v-for="(item, index) in splitToArray(personalList?.certificateNames)"
          :key="index"
          class="bg-#F3F3F3 border-rd-6rpx h-46rpx min-w-150rpx px-20rpx center"
        >
          <text class="c-#333333 text-24rpx">{{ item }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { splitToArray, formatToKilo } from '@/utils'
import { DICT_IDS } from '@/enum'
import { UserInfoWithPresence } from '@/ChatUIKit/types'
import type { hrIndexResumeUserListInt } from '@/service/hrIndex/types'
import type { hrPositionQueryOptionListInt } from '@/service/hrPosition/types'
import avatarOne from '@/static/common/avatar/1.png'
import postMark from '@/static/common/post-mark.png'

interface propsInt {
  list: hrIndexResumeUserListInt
  position: hrPositionQueryOptionListInt
}
const props = withDefaults(defineProps<propsInt>(), {
  list: () => ({}) as hrIndexResumeUserListInt,
})
const { getDictLabel } = useDictionary()
const {
  bool: skeletonLoading,
  setTrue: setSkeletonLoadingTrue,
  setFalse: setSkeletonLoadingFalse,
} = useBoolean()
const { bool: isWorkExperienceExpanded, toggle: toggleWorkExperience } = useBoolean()

const personOtherInfo = reactive({
  /** 学历 */
  qualification: '',
  /** 求职状态 */
  seekStatus: '',
})
const personalList = computed(() => props.list)
const hxUserInfo = ref<Partial<UserInfoWithPresence>>({})
async function getHxUserInfo() {
  try {
    setSkeletonLoadingTrue()
    const { hxUserInfoVO } = personalList.value
    const { username } = hxUserInfoVO
    try {
      await uni.$UIKit.appUserStore.getUsersPresenceFromServer({
        userIdList: [username],
      })
    } catch (error) {
      console.log('获取在线状态失败', error)
    }

    await uni.$UIKit.appUserStore.getUsersInfoFromServer({
      userIdList: [username],
    })
    const userInfo = uni.$UIKit.appUserStore.getUserInfoFromStore(username)
    hxUserInfo.value = userInfo
    setSkeletonLoadingFalse()
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}
function handleToPreviewDetail() {
  const { userId } = personalList.value
  const hrDetailItem = JSON.stringify({
    userId,
  })
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams('/resumeRelated/preview/index', {
      hrDetailItem,
    }),
  })
}
async function getQualification() {
  personOtherInfo.qualification = (await getDictLabel(
    DICT_IDS.EDUCATION_REQUIREMENT,
    personalList.value.qualification,
  )) as string
}
async function getSeekStatus() {
  personOtherInfo.seekStatus = (await getDictLabel(
    DICT_IDS.SEEK_STATUS,
    personalList.value.seekStatus,
  )) as string
}
onMounted(() => {
  getQualification()
  getSeekStatus()
  getHxUserInfo()
})
</script>

<style lang="scss" scoped>
//
.border-twinkle {
  position: relative;

  &::before {
    position: absolute;
    top: -2rpx;
    right: -2rpx;
    bottom: -2rpx;
    left: -2rpx;
    z-index: -1;
    content: '';
    background: linear-gradient(45deg, #0ea500, #00ff00, #0ea500);
    border-radius: 50rpx;
    animation: twinkle 2s infinite;
  }
}

.bg_left_icon_box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 86rpx;
  height: 86rpx;
  border: 3rpx solid #0ea500;
  border-radius: 50rpx;
}

.dot-separator {
  flex-shrink: 0;
  width: 6rpx;
  height: 6rpx;
  margin: 4rpx 11rpx 0 11rpx;
  background-color: #666;
  border-radius: 50%;
}
</style>
