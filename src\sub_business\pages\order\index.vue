<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '使用记录',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" v-model="pageData" @query="queryList" ref="pagingRef">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          custom-class="px-25rpx"
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">订单中心</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
      <view class="w-580rpx">
        <wd-tabs
          @change="handleChange"
          v-model="newsTabsStatus"
          :active-color="'#000'"
          :inactive-color="'#888888'"
          line-width="80rpx"
          line-height="10rpx"
          custom-class="custom-class"
        >
          <wd-tab
            v-for="(item, index) in newsTabsList"
            :key="item.name"
            :title="`${item.label}`"
            :name="index"
          />
        </wd-tabs>
      </view>
    </template>
    <view class="px-40rpx py-20rpx">
      <template v-for="item in pageData" :key="item.id">
        <orderList :item="item" />
      </template>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { type ConfigProviderThemeVars } from 'wot-design-uni'
import orderList from '@/sub_business/components/orderList.vue'
import { payDealList } from '@/service/order'

const { pageStyle, pageData, pageInfo, pageSetInfo, pagingRef } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const params = ref({
  entity: {
    dealState: null,
  },
  orderBy: {},
  page: 1,
  size: 10,
})
const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res = await payDealList({ ...params.value, page: pageInfo.page, size: pageInfo.size })
  console.log(res, 'res=============')
  if (res.code === 0) {
    pagingRef.value.complete(res.data.list)
  }
}
const handleChange = ({ index }) => {
  console.log(index, 'index=============')
  if (index === 0) {
    params.value.entity.dealState = null
  } else if (index === 1) {
    params.value.entity.dealState = 0
  } else if (index === 2) {
    params.value.entity.dealState = 1
  } else if (index === 3) {
    params.value.entity.dealState = 6
  }
  pagingRef.value.reload()
}
onLoad(async (options) => {
  await uni.$onLaunched
  pagingRef.value.reload()
})
function handleClickLeft() {
  uni.navigateBack()
}
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
  navbarColor: '#000',
}
const newsTabsStatus = ref(0)
const newsTabsList = ref([
  { name: '1', label: '全部' },
  { name: '2', label: '待付款' },
  { name: '3', label: '进行中' },
  { name: '4', label: '已完成' },
])
</script>

<style lang="scss" scoped>
:deep(.custom-class) {
  background-color: transparent;

  .wd-tabs__nav {
    background-color: transparent;
  }
}

:deep(.wd-tabs__line) {
  background: #ff4545 !important;
}
</style>
