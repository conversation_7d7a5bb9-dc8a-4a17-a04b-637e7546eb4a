import { KEYWORDS_DETECTION, USER_TYPE } from '@/enum'

/** 关键词检测 */
export const useKeywordDetection = () => {
  const { userIntel } = useUserInfo()
  const cache = new Map<string, ReturnType<typeof preprocessText>>()
  const kmpTableCache = new Map<string, number[]>()

  const HOMOPHONE_MAP = new Map([
    ['0', 'o零〇洞'],
    ['1', 'i一壹l丨I'],
    ['2', 'z二贰两俩Z'],
    ['3', '三叁仨'],
    ['4', '四肆'],
    ['5', '五伍wu'],
    ['6', '六陆liu'],
    ['7', '七柒qi'],
    ['8', '八捌ba'],
    ['9', '九玖jiu'],
    ['a', '@艾爱A'],
    ['o', '0零〇洞O'],
    ['i', '1一壹l丨I'],
    ['l', '1一壹i丨L'],
    ['s', '$S'],
    ['z', '2二贰Z'],
    ['x', '×乘X'],
    ['q', 'Q'],
  ])
  const NOISE_PATTERNS = [
    /\s|\u200B|\u200C|\u200D|\uFEFF/g,
    /[.。,，;；:：!！?？]/g,
    /[-_=+*#@&%]/g,
    /[0-9]/g,
  ]
  /** 匹配类型枚举 */
  const MATCH_TYPES = {
    EXACT: { type: 'exact', confidence: 1.0 },
    NORMALIZED: { type: 'normalized', confidence: 0.95 },
    HOMOPHONE: { type: 'homophone', confidence: 0.9 },
    SEQUENCE: { type: 'sequence', confidence: 0.85 },
    FUZZY: { type: 'fuzzy', baseConfidence: 0.8 },
    COMBINATION: { type: 'combination', multiplier: 0.9 },
  } as const

  /** 合法上下文模式 */
  const LEGITIMATE_CONTEXT_PATTERNS = {
    微信: [
      /我们?可以加个?微信/,
      /交换一?下微信/,
      /微信联系/,
      /有微信吗/,
      /微信号/,
      /发个?微信/,
      /微信聊/,
      /微信方便/,
      /留个?微信/,
      /通过微信/,
      /微信沟通/,
      /微信详聊/,
      /微信交流/,
      /微信讨论/,
      /方便微信/,
      /微信谈/,
    ],
    qq: [
      /我们?可以加个?qq/,
      /交换一?下qq/,
      /qq联系/,
      /有qq吗/,
      /qq号/,
      /发个?qq/,
      /qq聊/,
      /通过qq/,
      /qq沟通/,
      /qq详聊/,
      /qq交流/,
      /方便qq/,
    ],
    手机: [
      /手机方便/,
      /手机联系/,
      /留个?手机号/,
      /手机号码/,
      /交换一?下手机号/,
      /我们?可以交换手机号/,
      /能交换个?手机号/,
      /交换个?联系方式/,
      /互换手机号/,
      /换个?手机号/,
      /有手机号吗/,
      /手机号方便/,
      /通过手机/,
      /手机沟通/,
      /手机详聊/,
      /手机谈/,
      /发个?手机/,
    ],
    手机号: [
      /交换一?下手机号/,
      /我们?可以交换手机号/,
      /能交换个?手机号/,
      /互换手机号/,
      /换个?手机号/,
      /有手机号吗/,
      /手机号方便/,
      /留个?手机号/,
      /发个?手机号/,
      /手机号联系/,
      /手机号沟通/,
      /手机号详聊/,
      /给个?手机号/,
    ],
    联系方式: [
      /交换个?联系方式/,
      /我们?可以交换联系方式/,
      /留个?联系方式/,
      /互换联系方式/,
      /有联系方式吗/,
      /联系方式方便/,
      /其他联系方式/,
      /给个?联系方式/,
      /发个?联系方式/,
      /联系方式详聊/,
      /便于联系方式/,
      /方便联系方式/,
    ],
    电话: [
      /电话联系/,
      /电话沟通/,
      /电话详聊/,
      /电话方便/,
      /打个?电话/,
      /电话谈/,
      /通过电话/,
      /电话交流/,
      /电话号码/,
      /有电话吗/,
      /留个?电话/,
      /发个?电话/,
    ],
    私聊: [
      /我们?私聊/,
      /可以私聊/,
      /私聊详聊/,
      /私聊一下/,
      /私聊沟通/,
      /私聊谈/,
      /私聊交流/,
      /方便私聊/,
      /私聊讨论/,
    ],
    加好友: [/可以加个?好友/, /我们?加个?好友/, /互加好友/, /加好友聊/, /加好友详聊/, /加好友交流/],
    联系: [
      /联系详聊/,
      /联系沟通/,
      /联系交流/,
      /联系谈/,
      /方便联系/,
      /保持联系/,
      /随时联系/,
      /联系讨论/,
    ],
    沟通: [
      /深入沟通/,
      /详细沟通/,
      /进一步沟通/,
      /线下沟通/,
      /当面沟通/,
      /电话沟通/,
      /视频沟通/,
      /面对面沟通/,
    ],
    详聊: [/我们?详聊/, /可以详聊/, /方便详聊/, /线下详聊/, /电话详聊/, /微信详聊/, /qq详聊/],
  }

  /** 检查是否为合法上下文使用 */
  const isLegitimateContext = (content: string, keyword: string): boolean => {
    const patterns = LEGITIMATE_CONTEXT_PATTERNS[keyword.toLowerCase()]
    if (!patterns) return false

    return patterns.some((pattern) => pattern.test(content))
  }

  /** 短词过滤规则 */
  const shouldSkipShortWord = (keyword: string, content: string, confidence: number): boolean => {
    if (keyword.length <= 3) {
      if (isLegitimateContext(content, keyword)) {
        return true
      }
      if (confidence < 0.9) {
        return true
      }
    }

    return false
  }

  const preprocessText = (text: string) => {
    if (!text) {
      return {
        original: '',
        normalized: '',
        homophone: '',
        chinese: '',
        alphanumeric: '',
      }
    }
    if (cache.has(text)) {
      return cache.get(text)!
    }
    const original = text.toLowerCase()
    let normalized = original
    normalized = NOISE_PATTERNS.reduce((acc, pattern) => acc.replace(pattern, ''), normalized)

    let homophone = normalized
    for (const [target, sources] of Array.from(HOMOPHONE_MAP.entries())) {
      const sourceChars = sources.split('')
      for (const char of sourceChars) {
        if (char.length === 1 && normalized.includes(char)) {
          const regex = new RegExp(char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
          homophone = homophone.replace(regex, target)
        }
      }
    }

    const result = {
      original,
      normalized,
      homophone,
      chinese: normalized.replace(/[^a-z\u4e00-\u9fa5]/g, ''),
      alphanumeric: normalized.replace(/[^\w]/g, ''),
    }
    cache.set(text, result)
    return result
  }

  /** KMP算法 */
  const buildKMPTable = (pattern: string): number[] => {
    if (kmpTableCache.has(pattern)) {
      return kmpTableCache.get(pattern)!
    }

    const table = new Array(pattern.length).fill(0)
    let j = 0

    for (let i = 1; i < pattern.length; i++) {
      while (j > 0 && pattern[i] !== pattern[j]) {
        j = table[j - 1]
      }
      if (pattern[i] === pattern[j]) {
        j++
      }
      table[i] = j
    }

    kmpTableCache.set(pattern, table)
    return table
  }

  const kmpSearch = (text: string, pattern: string): boolean => {
    if (!pattern || !text || pattern.length > text.length) return false

    const table = buildKMPTable(pattern)
    let j = 0

    for (let i = 0; i < text.length; i++) {
      while (j > 0 && text[i] !== pattern[j]) {
        j = table[j - 1]
      }
      if (text[i] === pattern[j]) {
        j++
      }
      if (j === pattern.length) {
        return true
      }
    }
    return false
  }

  /** 序列匹配算法 */
  const sequenceMatch = (text: string, pattern: string, maxGap: number = 3): boolean => {
    if (!text || !pattern || pattern.length < 2 || text.length < pattern.length) return false

    let patternIndex = 0
    let lastMatchPos = -1

    for (let i = 0; i < text.length && patternIndex < pattern.length; i++) {
      if (text[i] === pattern[patternIndex]) {
        if (lastMatchPos >= 0 && i - lastMatchPos - 1 > maxGap) {
          patternIndex = text[i] === pattern[0] ? 1 : 0
        } else {
          patternIndex++
        }
        lastMatchPos = i
      }
    }

    return patternIndex === pattern.length
  }

  /** 编辑距离算法 */
  const levenshteinDistance = (s1: string, s2: string, maxDistance?: number): number => {
    if (s1.length === 0) return s2.length
    if (s2.length === 0) return s1.length

    if (maxDistance && Math.abs(s1.length - s2.length) > maxDistance) {
      return maxDistance + 1
    }

    let prev = Array(s2.length + 1)
      .fill(0)
      .map((_, i) => i)
    let curr = Array(s2.length + 1).fill(0)

    for (let i = 1; i <= s1.length; i++) {
      curr[0] = i
      let minInRow = i

      for (let j = 1; j <= s2.length; j++) {
        if (s1[i - 1] === s2[j - 1]) {
          curr[j] = prev[j - 1]
        } else {
          curr[j] = Math.min(prev[j] + 1, curr[j - 1] + 1, prev[j - 1] + 1)
        }
        minInRow = Math.min(minInRow, curr[j])
      }
      if (maxDistance && minInRow > maxDistance) {
        return maxDistance + 1
      }

      ;[prev, curr] = [curr, prev]
    }

    return prev[s2.length]
  }

  const splitWordMatch = (
    text: string,
    keywords: string[],
  ): { matched: boolean; keyword: string; confidence: number } => {
    if (!text || !keywords?.length) return { matched: false, keyword: '', confidence: 0 }

    const textChars = text.replace(/\s+/g, '').split('')

    for (const keyword of keywords) {
      const keywordChars = keyword.split('')

      let matchedChars = 0
      let lastIndex = -1

      for (const char of keywordChars) {
        const charIndex = textChars.indexOf(char, lastIndex + 1)
        if (charIndex > lastIndex) {
          matchedChars++
          lastIndex = charIndex
        }
      }

      const matchRatio = matchedChars / keywordChars.length
      if (matchRatio >= 0.8 && matchedChars >= 3) {
        return {
          matched: true,
          keyword,
          confidence: matchRatio * 0.9,
        }
      }
    }

    return { matched: false, keyword: '', confidence: 0 }
  }

  /** 词汇拆分匹配 */
  const wordDecompositionMatch = (
    content: ReturnType<typeof preprocessText>,
    keywords: string[],
  ): { matched: boolean; keyword: string; confidence: number } => {
    if (!keywords || keywords.length === 0) {
      return { matched: false, keyword: '', confidence: 0 }
    }
    const validKeywords = keywords.filter((keyword) => keyword && keyword.length >= 2)
    return splitWordMatch(content.normalized, validKeywords)
  }

  const isMatch = (
    content: ReturnType<typeof preprocessText>,
    keyword: string,
  ): { matched: boolean; confidence: number; type: string } => {
    if (!keyword?.trim()) {
      return { matched: false, confidence: 0, type: 'none' }
    }

    const processedKeyword = preprocessText(keyword)

    if (!content.normalized && !content.original) {
      return { matched: false, confidence: 0, type: 'none' }
    }
    if (
      content.original.includes(keyword) ||
      content.normalized.includes(processedKeyword.normalized)
    ) {
      return { matched: true, confidence: 1.0, type: 'direct_include' }
    }

    const forwardStrategies = [
      {
        check: () => content.original && kmpSearch(content.original, processedKeyword.original),
        result: MATCH_TYPES.EXACT,
      },
      {
        check: () =>
          content.normalized && kmpSearch(content.normalized, processedKeyword.normalized),
        result: MATCH_TYPES.NORMALIZED,
      },
      {
        check: () => content.homophone && kmpSearch(content.homophone, processedKeyword.homophone),
        result: MATCH_TYPES.HOMOPHONE,
      },
      {
        check: () =>
          content.normalized && sequenceMatch(content.normalized, processedKeyword.normalized),
        result: MATCH_TYPES.SEQUENCE,
      },
    ]
    const reverseStrategies = [
      {
        check: () =>
          processedKeyword.original && kmpSearch(processedKeyword.original, content.original),
        result: { ...MATCH_TYPES.EXACT, confidence: 0.9 },
      },
      {
        check: () =>
          processedKeyword.normalized && kmpSearch(processedKeyword.normalized, content.normalized),
        result: { ...MATCH_TYPES.NORMALIZED, confidence: 0.85 },
      },
      {
        check: () =>
          processedKeyword.homophone && kmpSearch(processedKeyword.homophone, content.homophone),
        result: { ...MATCH_TYPES.HOMOPHONE, confidence: 0.8 },
      },
      {
        check: () =>
          processedKeyword.normalized &&
          sequenceMatch(processedKeyword.normalized, content.normalized),
        result: { ...MATCH_TYPES.SEQUENCE, confidence: 0.75 },
      },
    ]

    for (const strategy of forwardStrategies) {
      if (strategy.check()) {
        return {
          matched: true,
          confidence: strategy.result.confidence,
          type: strategy.result.type,
        }
      }
    }

    for (const strategy of reverseStrategies) {
      if (strategy.check()) {
        return {
          matched: true,
          confidence: strategy.result.confidence,
          type: `partial_${strategy.result.type}`,
        }
      }
    }

    if (processedKeyword.normalized.length >= 2 && content.normalized.length >= 2) {
      const maxLen = Math.max(processedKeyword.normalized.length, content.normalized.length)
      const minLen = Math.min(processedKeyword.normalized.length, content.normalized.length)
      if (maxLen / minLen > 2.5) {
        return { matched: false, confidence: 0, type: 'none' }
      }

      const maxDistance = Math.ceil(Math.min(maxLen * 0.4, minLen * 0.6))
      const distance = levenshteinDistance(
        content.normalized,
        processedKeyword.normalized,
        maxDistance,
      )

      if (distance <= maxDistance) {
        const similarity = 1 - distance / maxLen
        let threshold = 0.6
        if (processedKeyword.normalized.length <= 2) {
          threshold = 0.8
        } else if (processedKeyword.normalized.length <= 4) {
          threshold = 0.7
        }
        const commonChars = new Set(
          [...content.normalized].filter((char) => processedKeyword.normalized.includes(char)),
        ).size
        const charRatio =
          commonChars / Math.min(content.normalized.length, processedKeyword.normalized.length)

        if (similarity >= threshold && charRatio >= 0.2) {
          return {
            matched: true,
            confidence: similarity * MATCH_TYPES.FUZZY.baseConfidence * Math.max(charRatio, 0.5),
            type: MATCH_TYPES.FUZZY.type,
          }
        }
      }
    }

    return { matched: false, confidence: 0, type: 'none' }
  }
  /** 组合匹配检测 */
  const checkCombinationMatch = (
    content: ReturnType<typeof preprocessText>,
    keywords: string[],
  ): { matched: boolean; confidence: number; matchedKeywords: string[] } => {
    if (!keywords || keywords.length === 0) {
      return { matched: false, confidence: 0, matchedKeywords: [] }
    }
    const matches = keywords
      .filter((keyword) => keyword?.trim())
      .map((keyword) => ({
        keyword,
        result: isMatch(content, keyword),
      }))
      .filter((item) => {
        if (!item.result.matched) return false
        if (shouldSkipShortWord(item.keyword, content.original, item.result.confidence)) {
          return false
        }

        return true
      })

    const matchedKeywords = matches.map((item) => item.keyword)

    if (matches.length >= 2) {
      const avgConfidence =
        matches.reduce((sum, match) => sum + match.result.confidence, 0) / matches.length

      return {
        matched: true,
        confidence: avgConfidence * MATCH_TYPES.COMBINATION.multiplier,
        matchedKeywords,
      }
    }
    if (matches.length === 1) {
      const match = matches[0]
      if (match.result.confidence >= 0.95) {
        return {
          matched: true,
          confidence: match.result.confidence * 0.8,
          matchedKeywords,
        }
      }
    }
    const decompositionResult = wordDecompositionMatch(content, keywords)
    if (decompositionResult.matched) {
      if (
        !shouldSkipShortWord(
          decompositionResult.keyword,
          content.original,
          decompositionResult.confidence,
        )
      ) {
        return {
          matched: true,
          confidence: decompositionResult.confidence,
          matchedKeywords: [decompositionResult.keyword],
        }
      }
    }

    return { matched: false, confidence: 0, matchedKeywords: [] }
  }

  /** 关键词检测提醒 */
  const keywordDetectionReminder = (content: string) => {
    if (!content?.trim() || !userIntel?.value?.type) return null
    const processedContent = preprocessText(content)
    let globalBestMatch = { keyword: '', confidence: 0, type: '', category: '' }
    for (const [category, keywords] of Object.entries(KEYWORDS_DETECTION.VIOLATION_KEYWORDS)) {
      if (!Array.isArray(keywords) || keywords.length === 0) continue
      const sortedKeywords = [...keywords]
        .filter((keyword) => keyword?.trim())
        .sort((a, b) => b.length - a.length)

      console.log(`🔍 检测分类: ${category}, 关键词数量: ${sortedKeywords.length}`)

      for (const keyword of sortedKeywords) {
        const result = isMatch(processedContent, keyword)
        if (result.matched) {
          if (shouldSkipShortWord(keyword, content, result.confidence)) {
            console.log(`⏭️  跳过短词: "${keyword}", 原因: 合法上下文或置信度不足`)
            continue
          }

          console.log(
            `✅ 匹配到关键词: "${keyword}", 置信度: ${result.confidence}, 类型: ${result.type}`,
          )
          if (result.confidence > globalBestMatch.confidence) {
            globalBestMatch = {
              keyword,
              confidence: result.confidence,
              type: result.type,
              category,
            }
          }
          if (result.confidence >= 0.95) break
        }
      }
      const combinationResult = checkCombinationMatch(processedContent, sortedKeywords)
      if (combinationResult.matched) {
        console.log(
          `✅ 组合匹配成功: ${combinationResult.matchedKeywords.join(', ')}, 置信度: ${combinationResult.confidence}`,
        )
        if (combinationResult.confidence > globalBestMatch.confidence) {
          globalBestMatch = {
            keyword: `组合关键词: ${combinationResult.matchedKeywords.join(', ')}`,
            confidence: combinationResult.confidence,
            type: MATCH_TYPES.COMBINATION.type,
            category,
          }
        }
      }
      if (globalBestMatch.confidence >= 1.0) break
    }

    console.log('🎯 最终检测结果:', globalBestMatch)
    if (globalBestMatch.confidence > 0.5) {
      console.log(`🔔 关键词检测提醒: ${globalBestMatch.category}`)
      const currentUserMessage =
        KEYWORDS_DETECTION.VIOLATION_MESSAGES[userIntel.value.type]?.[globalBestMatch.category]
      return {
        category: globalBestMatch.category,
        keyword: globalBestMatch.keyword,
        message: currentUserMessage,
        messages: {
          [USER_TYPE.HR]:
            KEYWORDS_DETECTION.VIOLATION_MESSAGES[USER_TYPE.HR]?.[globalBestMatch.category],
          [USER_TYPE.APPLICANT]:
            KEYWORDS_DETECTION.VIOLATION_MESSAGES[USER_TYPE.APPLICANT]?.[globalBestMatch.category],
        },
        confidence: globalBestMatch.confidence,
        matchType: globalBestMatch.type,
        processedContent: processedContent.normalized,
      }
    }
    return null
  }

  return {
    keywordDetectionReminder,
  }
}
