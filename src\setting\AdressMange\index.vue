<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="居住地址"></CustomNavBar>
    </template>
    <view class="pageContaner">
      <view class="pageContaner-position flex-between">
        <wd-textarea
          no-border
          v-model="adreesName"
          placeholder="定位我的地点"
          auto-height
          custom-class="custom-class"
          confirm-type="send"
          @confirm="confirm"
        />
        <view class="pageContaner-right" @click="getLocationInfo">
          <view class="pageContaner-right-c">
            <wd-img :width="13" :height="13" :src="position" />
          </view>
          <view class="text-20rpx c-#000">点击定位</view>
        </view>
      </view>
      <view class="jobDetail-image-list" v-if="imgMap">
        <image class="jobDetail-image" :src="imgMap"></image>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box" @click="submit">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { saveMyAddress } from '@/interPost/my'
import { queryKey } from '@/interPost/home'
import { geographicToAddress, addressToGeographic } from '@/interPost/common'
import position from '@/setting/img/position.png'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const adrees = reactive({
  homeLocation: '',
  lat: 0,
  lon: 0,
})
const adreesName = ref('')
const imgMap = ref('')
const staticKey = ref('')
// 提交
const submit = async () => {
  console.log(adrees, 'adrees====')
  if (!adrees.lat || !adrees.lon || !adrees.homeLocation) {
    uni.showToast({
      title: '请检查定位，定位失败',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  await confirm()
  const res: any = await saveMyAddress({ ...adrees })
  if (res.code === 0) {
    uni.navigateBack({
      delta: 1,
      success() {
        uni.$emit('refresh-adress')
        uni.$emit('refresh-a-jobList')
      },
    })
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// 获取经纬度
const getLocationInfo = () => {
  uni.getLocation({
    type: 'wgs84',
    success: function (res) {
      console.log(res, 'res定位===')
      const lat = res.latitude
      const lon = res.longitude
      adrees.lat = lat
      adrees.lon = lon
      if (lat && lon) {
        geographicToAddressList()
      }
    },
    fail: function () {
      uni.showToast({
        title: '获取定位失败',
        icon: 'none',
        duration: 3000,
      })
    },
  })
}
// 获取ket
const getMapKet = async () => {
  const res: any = await queryKey()
  if (res.code === 0) {
    staticKey.value = res.data.staticKey
  }
}
// 根据经纬度生成地址
const geographicToAddressList = async () => {
  const res: any = await geographicToAddress({ lat: adrees.lat, lon: adrees.lon, ver: '31' })
  if (res.code === 0) {
    adreesName.value = res.data.formatted_address ? res.data.formatted_address : ''
    const lat = res.data.location.lat
    const lon = res.data.location.lon
    adrees.homeLocation = res.data.formatted_address
    if (lat && lon) {
      imgMap.value = `https://api.tianditu.gov.cn/staticimage?center=${lon},${lat}&width=300&height=200&zoom=12&tk=${staticKey.value}&markers=${lon},${lat}`
    }
  }
}
// 地址换经纬度
const confirm = async () => {
  const res: any = await addressToGeographic({ address: adreesName.value })
  console.log(res, '地址换经纬度')
  if (res.code === 0) {
    if (res.data.lat && res.data.lon) {
      adreesName.value = res.data.keyWord ? res.data.keyWord : ''
      adrees.lat = res.data.lat
      adrees.lon = res.data.lon
      adrees.homeLocation = res.data.keyWord
      imgMap.value = `https://api.tianditu.gov.cn/staticimage?center=${adrees.lon},${adrees.lat}&width=300&height=200&zoom=12&tk=${staticKey.value}&markers=${adrees.lon},${adrees.lat}`
    } else {
      uni.showToast({
        title: '获取定位失败,请重新输入',
        icon: 'none',
        duration: 3000,
      })
    }
  } else {
    uni.showToast({
      title: '获取定位失败,请重新输入',
      icon: 'none',
      duration: 3000,
    })
  }
}
onLoad(async (options) => {
  await getMapKet()
  if (options.lon && options.lat) {
    adrees.lat = options.lat
    adrees.lon = options.lon
    console.log(adrees.lon, adrees.lat, 'adrees.lon, adrees.lat====lalal')
    adreesName.value = options.homeLocation
    adrees.homeLocation = options.homeLocation
    if (adrees.lon && adrees.lat) {
      imgMap.value = `https://api.tianditu.gov.cn/staticimage?center=${adrees.lon},${adrees.lat}&width=300&height=200&zoom=12&tk=${staticKey.value}&markers=${adrees.lon},${adrees.lat}`
    }
  } else {
    await getLocationInfo()
  }
})
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  background-color: transparent;
}
.custom-class {
  width: 100%;
}
.pageContaner {
  padding: 40rpx;
  .pageContaner-position {
    padding: 15rpx 40rpx;
    background: #fff;
    border-radius: 20rpx;
    .pageContaner-right {
      .pageContaner-right-c {
        width: 88rpx;
        height: 54rpx;
        line-height: 54rpx;
        text-align: center;
        background: #f0f0f0;
        border-radius: 10rpx;
      }
    }
  }
}
.jobDetail-image-list {
  margin: 40rpx 0rpx 60rpx;

  .jobDetail-image {
    width: 100%;
    height: 320rpx;
  }
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
