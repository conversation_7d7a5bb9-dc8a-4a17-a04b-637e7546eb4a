<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="求职意向">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
      </CustomNavBar>
    </template>
    <view class="container">
      <view class="container-box">
        <view class="flex-between border-b p-b-20rpx">
          <view class="p-l-40rpx text-30rpx font-600">求职意向</view>
          <wd-icon
            name="add-circle1"
            @click="goSeekEmployment"
            class="p-r-40rpx"
            color="#000000"
            size="23px"
          ></wd-icon>
        </view>
        <view
          class="flex-between p-t-30rpx"
          v-for="(item, index) in jobIntentionListFull"
          :key="index"
          @click="goEditSeekEmployment(item)"
        >
          <view class="p-l-40rpx p-r-40rpx">
            <view class="flex-c">
              <view class="m-r-20rpx text-28rpx c-#000">{{ item.expectedPositions }}</view>
              <view class="text-28rpx c-#000">
                <text>{{ item.salaryExpectationStart }}</text>
                <text v-if="item.salaryExpectationEnd">-</text>
                <text v-if="item.salaryExpectationEnd">{{ item.salaryExpectationEnd }}</text>
              </view>
            </view>
            <view class="flex-c p-t-10rpx">
              <view class="m-r-20rpx c-#888888 text-24rpx">{{ item.provinceName }}</view>
              <view class="c-#888888 text-24rpx m-r-20rpx flex-1">
                {{ item.expectedIndustry === '不限' ? '行业不限' : item.expectedIndustry }}
              </view>
              <view class="c-#888888 text-24rpx">
                {{ item.jobType === 1 ? '全职' : item.jobType === 2 ? '兼职' : '实习' }}
              </view>
            </view>
          </view>

          <wd-icon class="p-r-40rpx" name="chevron-right" color="#333333" size="20px"></wd-icon>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryFindJob } from '@/interPost/home'
import { numberTokw } from '@/utils/common'
import { useLoginStore } from '@/store'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const loginStore = useLoginStore()
const jobIntentionListFull = ref([])
const isShow = ref('')
// 列表
const queryFindJobList = async () => {
  const res: any = await queryFindJob()
  if (res.code === 0) {
    res.data.forEach((ele) => {
      ele.salaryExpectationStart =
        ele.salaryExpectationStart === 0 ? '面议' : numberTokw(ele.salaryExpectationStart + '')
      ele.salaryExpectationEnd =
        ele.salaryExpectationEnd === 0 ? '' : numberTokw(ele.salaryExpectationEnd + '')
    })
    jobIntentionListFull.value = res.data
  }
  console.log(res, 'res====')
}
// 求职期望
const goSeekEmployment = (index: number) => {
  uni.navigateTo({
    url: `/resumeRelated/seekEmployment/index?isAdd=add`,
  })
  loginStore.setjobArry([
    {
      name: '不限',
      code: 0,
    },
  ])
}
// 编辑求职期望
const goEditSeekEmployment = (item: any) => {
  const str = JSON.stringify(item)
  try {
    const expectedIndustryList = item.expectedIndustry.split(',')
    const expectedIndustryCodeList = item.expectedIndustryCode.split(',')
    const industryList = expectedIndustryList.map((name: string, key: number) => {
      return {
        name,
        code: Number(expectedIndustryCodeList[key]),
      }
    })
    loginStore.setjobArry(industryList)
  } catch (error) {}
  uni.navigateTo({
    url: `/resumeRelated/seekEmployment/index?item=${encodeURIComponent(str)}&isAdd=edit`,
  })
}
// 返回
const back = () => {
  if (isShow.value === 'home') {
    uni.navigateBack({
      delta: 1,
      success() {
        uni.$emit('refresh-a-jobList') // 触发刷新事件
      },
    })
  } else {
    uni.navigateBack()
  }
}
onLoad((options) => {
  isShow.value = options.isShow ? options.isShow : ''
})
onShow(async () => {
  await queryFindJobList()
})
</script>
<style scoped lang="scss">
.border-b {
  border-bottom: 1rpx solid rgba(237, 230, 230, 1);
}
.container {
  padding: 40rpx 40rpx 40rpx;
  .container-box {
    padding: 30rpx 0rpx 30rpx;
    background: #fff;
    border-radius: 30rpx;
    box-shadow: 8rpx 8rpx 32rpx 0 rgba(0, 0, 0, 0.1);
  }
}
.jobExpectations-qw {
  line-height: 60rpx;
}

.jobExpectations-exepress {
  padding-bottom: 20rpx;

  .jobExpectations-exepress-left {
    .text-name {
      line-height: 60rpx;
      color: #000000;
    }

    .text-salary {
      padding-left: 20rpx;
    }

    .text-position {
      font-size: 24rpx;
      color: #888888;
    }

    .text-denery {
      padding-left: 40rpx;
    }
  }
}
</style>
