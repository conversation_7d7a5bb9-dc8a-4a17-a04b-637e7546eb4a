<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="agreement-container">
      <view class="header">
        <text class="title">易直聘用户协议</text>
        <text class="subtitle">版本：ver202505 生效日期：2025年05月24日</text>
      </view>

      <view class="content-list">
        <view
          class="list-item"
          v-for="(item, index) in contentList"
          :key="index"
          @click="scrollToSection(index)"
        >
          <text class="list-text">{{ item }}</text>
        </view>
      </view>

      <view class="content">
        <text class="paragraph">尊敬的用户：</text>
        <text class="paragraph">
          欢迎加入易直聘并体验我们的服务。在您正式成为易直聘的注册用户并开始使用我们的服务之前，请务必仔细阅读《易直聘用户协议》（以下简称“协议”），以便充分理解我们所提供的服务内容以及您所拥有的权利和义务。一旦您开始使用易直聘的服务，表示您已完全同意并接受了本协议的所有条款。
          本协议系由您（以下简称“用户”或“您”）与重庆中誉易职网络信息技术有限公司（以下简称“易直聘”或“我们”）（（重庆市九龙坡区渝州路街道科园一路200号C-17层02号））提供的易直聘应用（网页，移动应用和小程序，以下简称“本平台”或“易直聘”）所订立的相关权利义务规范。请您在注册并使用易直聘之前，认真阅读以下条款。
        </text>
        <view class="section" v-for="(section, idx) in sections" :id="`section-${idx}`" :key="idx">
          <text class="section-title">{{ section.title }}</text>
          <view v-for="(p, pidx) in section.paragraphs" :key="pidx">
            <text class="paragraph">{{ p }}</text>
          </view>
        </view>
        <view class="footer">
          <text class="footer-text">生效日期：2025年05月24日</text>
          <text class="footer-text">版本：ver202505</text>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const pagingRef = ref(null)
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const contentList = ref([
  '一、注册条款的接受',
  '二、用户的注册与认证',
  '三、用户账号及安全',
  '四、服务说明',
  '五、有限责任条款',
  '六、用户的个人信息权利',
  '七、用户的平台使用义务',
  '八、违约责任',
  '九、关于用户在易直聘上传或张贴的内容',
  '十、关于面试聊天等即时通讯服务',
  '十一、注销服务',
  '十二、本协议条款的变更和修改',
  '十三、不可抗力',
  '十四、通知',
  '十五、法律的适用和管辖',
])

const sections = ref([
  {
    title: '一、注册条款的接受',
    paragraphs: [
      '根据《网络安全法》和《互联网用户账号信息管理规定》等相关法律法规的规定，您在易直聘的注册页面通过手机短信验证并选择接受易直聘的《用户协议》及其他相关协议，表示您已经阅读并同意与易直聘签订本协议，成为易直聘的用户，并且接受本协议内所有注册条款以及易直聘的《隐私政策》和平台内公布的《招聘行为管理规范》、《易直聘增值服务协议》等各项协议或规则（统称为“平台协议”或“平台规则”）的全部条款约束，包括争议解决条款（详见本协议第十五条）。',
    ],
  },
  {
    title: '二、用户的注册与认证',
    paragraphs: [
      '1.用户在注册易直聘账号时需满足以下条件：注册时及使用易直聘服务期间，必须以招聘或求职为目的；注册时年龄需满16周岁或以上。',
      '2.为充分体验易直聘平台提供的服务，用户须遵循易直聘的注册流程，提供真实、准确、最新且完整的个人信息，并承诺所提供信息不存在误导性陈述或重大遗漏；成为招聘者并完成认证的用户（包括企业雇主和人力资源机构），需提供最新单位信息、相关许可证、职位详情、企业邮箱、授权书、承诺函等资料，并保证这些信息的真实有效；用户需确保其职务行为，如发布招聘信息、与求职者交流、获取和使用求职者简历等，在使用平台期间始终有效；认证用户需确保其招聘账号与授权单位保持唯一对应关系。',
      '3.根据《网络招聘服务管理规定》及其他相关法律法规，若招聘用户提交的资料或信息不准确、不真实、不符合规范，或存在盗用、伪造证明材料的嫌疑，易直聘有权拒绝提供相关服务。这可能导致您无法使用易直聘软件或在使用中遇到功能限制。',
      '4.若用户以招聘为目的非法获取或使用求职者简历，将被视为严重违反协议，易直聘有权暂停或终止该用户账号并停止服务。对于虚假注册、发布虚假信息导致易直聘遭受经济损失或名誉损害的，易直聘保留追究法律责任的权利。',
    ],
  },
  {
    title: '三、用户账号及安全',
    paragraphs: [
      '1.用户需对其账号使用过程中产生的所有行为负全责，涵盖所有通过易直聘平台上传、发布、发送电子邮件或任何其他途径传递的信息、数据、文本、软件、音乐、音频、图片、图形、视频、消息或其他材料，无论这些内容是公开还是私下传递，内容发布者需承担相应责任。',
      '2.一旦用户账号出现未授权使用或其他安全问题，用户必须立即告知易直聘。若因用户操作不当或非易直聘原因导致账号信息泄露，用户需自行承担由此产生的不良后果。',
      '3.易直聘账号的所有权属于易直聘公司，用户完成注册流程后，仅获得账号的使用权，且该使用权仅限于账号的原始注册者。用户不得将账号转让、借出、出租、授权或出售给他人，也不得通过接受赠与、继承、租赁、转让或其他方式使他人使用易直聘账号。若我们有理由相信账号使用者非原始注册者，为确保账号安全，我们有权立即停止或终止对该账号的服务，并可永久封禁该账号。易直聘有权根据实际情况决定是否提前通知用户，且不承担因此造成的任何损失。',
      '4.用户不得主动向第三方透露账号信息或允许第三方使用账号，例如代为购买易直聘服务等。若因此导致其他用户隐私泄露、经济损失或平台损失，用户应承担全部责任。',
      '5.若您打算停用或更换当前账号绑定的手机号码，请务必先执行账号解绑操作。您可以通过【我的】-【设置】-【账号与安全中心】-【账号管理】来修改手机号码，如有疑问，请联系客服咨询。',
    ],
  },
  {
    title: '四、服务说明',
    paragraphs: [
      '1.易直聘利用互联网为客户提供网络服务，涵盖在线和离线相关业务。用户需自备上网所需设备，如电脑或移动设备、调制解调器或其他接入设备，并自行承担上网费用。',
      '2.在提供网络服务时，易直聘可能会对某些服务收取费用，届时会在相关页面明确标示。用户若不愿支付这些费用，则无法使用这些服务。付费服务将根据本注册条款之外的特定条款进行规定，详细说明付费服务内容及双方权利义务，包括但不限于《增值服务协议》和《发票管理规范》。用户须仔细阅读，一旦购买付费服务，即表示接受所有前述条款。',
      '3.无论是付费服务还是易直聘的免费服务，均设有有效期限，期满后服务将自动结束。除非协议另有说明，用户理解并同意，因数字化服务即时履行的特性，本条款排除适用《消费者权益保护法》第二十五条关于网络购买商品七日无理由退货的规定。用户在购买前应充分知悉并同意该条款，易直聘已通过显著方式进行提示，用户不得以未知悉为由主张退款。',
      '4.出于风险控制和求职安全的考量，易直聘可能要求部分用户补充提供资料（包括但不限于企业资质证明、承诺书、业务协议等），具体要求将在相关页面明确展示。用户若拒绝提供这些资料，易直聘有权根据情况暂停或终止向该用户提供部分或全部服务。',
      '5.易直聘将对授权等信息进行真实性审核和更新核验（目前，该审核机制包括“环境认证”和“线上审核”）。在审核过程中，用户应配合易直聘工作人员提供相关资料，包括但不限于营业执照、资质证书/相关业务协议、授权书、被授权人身份证信息等，并允许易直聘工作人员对其企业LOGO、办公环境进行审核及拍照备案。若用户拒绝，易直聘有权视情况暂停或终止为其提供部分或全部的服务。',
      '6.易直聘平台严禁任何非法活动，对于违反规定或严重违背招聘宗旨的用户，易直聘有权根据情况暂停或终止服务。用户发布的职位信息应严格限定在本协议签订时平台明示开通的服务地域范围内，跨境使用服务需另行签订补充协议。若用户行为导致易直聘受损，易直聘将保留追究法律责任的权利。',
      '7.易直聘有权利通过广告页面、电话、短信或邮件等方式，向用户发送服务相关的广告、促销信息及邀请参与活动等。用户有权利选择不接收除系统通知或重要信息外的其他信息，但用户理解并同意，涉及账户安全、服务变更、权利义务调整等重大事项的通知不属于可拒收范围。',
      '8.为提升招聘效率，易直聘在用户明示同意的前提下，可将用户发布的职位信息经脱敏处理后共享至关联及第三方合作平台。',
      '9.易直聘的虚拟道具等增值服务仅限于产品平台或由易直聘直接销售或赠送，用户不得在其他平台购买易直聘虚拟产品或服务。对于使用非官方虚拟产品或服务导致的损失，易直聘不承担责任；用户若因在其他平台充值或代充导致易直聘受损，应承担赔偿责任。易直聘有权对涉违规所得的虚拟道具、E币等予以冻结、作废或追缴，并可根据实际情况限制相关账户功能或注销账户以维护平台财产安全。',
      '10.用户应通过易直聘平台使用服务；未经许可，不得利用第三方工具或平台获取易直聘信息服务，包括登录账号、发布职位等。用户应积极配合易直聘采取的各项数据安全、风控、合规措施，因用户不配合导致的损失由用户自行承担。若因使用第三方软件导致信息泄露，易直聘不承担责任，用户需承担相应损失。',
      '11.招聘用户若向境外提供平台上获取的求职者简历，应遵守《个人信息保护法》等相关法律法规，确保个人信息合法出境。因用户违反信息出境合规义务导致易直聘被调查、处罚或索赔的，用户应承担全部责任并赔偿易直聘因此遭受的全部损失。',
    ],
  },
  {
    title: '五、有限责任条款',
    paragraphs: [
      '1.易直聘致力于向用户提供安全、及时、准确、高质量的服务，但不保证服务效果，也不承诺服务不会中断，也不对服务的及时性、安全性、准确性提供保证。平台展示的简历数量、职位匹配成功率等数据仅为算法估算值，不构成对服务效果的承诺。除非有特别约定，若无法使用服务或服务未达到预期效果，易直聘不承担任何责任。',
      '2.易直聘会根据国家规定，努力审核用户通过其服务传递的内容，但无法完全控制通过软件/网站服务传递的内容，不保证内容的准确性、完整性或品质。',
      '3.易直聘对于涉嫌违法或违反平台规定的内容，将根据法律法规及平台规则安排人工或自动化筛查，并在必要时行使删除、屏蔽或限制流转等权利。易直聘有权酌情确定内容管理及处置的具体流程和标准，用户理解并同意平台在合理范围内行使管理权利。',
      '4.用户理解并同意，平台对用户发布内容的算法推荐不构成对内容合法性的认可或担保。用户在使用易直聘服务时，可能会遇到不适当或令人反感的内容。在任何情况下，易直聘对此不承担任何责任。易直聘有权依法对相关内容进行筛查，包括通过人工智能、大数据分析等手段，可采取暂停服务、保存记录、向有关机关报告等措施。易直聘有权根据自身判断决定是否采取上述措施，且不因未采取措施而承担责任。',
      '5.对于易直聘提供的第三方广告信息、链接、资讯等（如有），易直聘不保证其内容的准确性、合法性或可靠性，相关责任由广告主承担；用户通过易直聘服务与广告主的联系或商业往来，完全属于用户和广告主之间的行为，与易直聘无关。用户与广告主的商业往来产生的任何损害或损失，易直聘不承担任何责任。',
      '6.易直聘已采取措施并努力审核用户上传的照片、资料、证件、视频、内容及图片等，但不保证其内容的正确性、合法性或可靠性，相关责任由上传内容的用户承担。',
      '7.用户应自行判断易直聘上其他用户发布的内容，并承担使用内容的所有风险，包括但不限于因对内容的正确性、完整性或实用性的依赖而产生的风险。易直聘无法且不会对因前述风险导致的任何损失或损害承担责任。',
      '8.用户应自行考虑是否使用软件/网站服务下载或获取任何资料，并自行承担风险，因下载任何资料导致的电脑系统损坏或数据丢失等后果，易直聘不承担任何责任。',
      '9.对于易直聘策划、发起、组织或承办的任何招聘相关活动（包括但不限于收费及完全公益的活动），易直聘不保证招聘效果，也不保证活动期间用户行为的合法性、合理性。由此产生的任何对用户个人或他人的人身、名誉或其他损害，应由行为实施主体承担责任。',
      '10.易直聘将努力核实和处理用户的投诉，并履行平台管理义务。根据国家相关法律法规的规定，易直聘有权决定是否向公众或被投诉者公开投诉内容。',
    ],
  },
  {
    title: '六、用户的个人信息权利',
    paragraphs: [
      '1.易直聘严格依照法律保护用户的个人资料和隐私。',
      '2.用户对自己的个人资料拥有以下权利，但不限于：',
      '2.1可随时要求查看和阅读，涉及国家安全、刑事侦查、公共利益保护等法定情形时，平台有权不予提供查询和阅览服务；',
      '2.2可随时要求补充或修正，除非在极个别情况下无法进行补充或修正。',
      '3.关于隐私政策的详细信息，请参阅易直聘的《隐私政策》。',
    ],
  },
  {
    title: '七、用户的平台使用义务',
    paragraphs: [
      '1.“平台使用”一词，在本协议中指的是用户在使用本平台提供的服务时所采取的所有行动，这包括但不限于用户注册、登录、进行认证、浏览聊天、管理账号、发布职位信息、邀请面试等，以及通过易直聘账号在本平台内进行的所有活动。',
      '2.用户在享受易直聘服务的过程中，必须遵循中国现行的法律法规以及特定行业的相关法律，这涵盖了《民法典》《个人信息保护法》《网络安全法》《数据安全法》《就业促进法》《劳动法》《劳动合同法》《商标法》《著作权法》《关于维护互联网安全的决定》《保守国家秘密法》《电信条例》《互联网信息服务管理办法》《计算机信息网络国际联网安全保护管理办法》《计算机信息系统安全保护条例》《计算机信息网络国际联网管理暂行规定》《网络招聘服务管理规定》《计算机信息系统国际联网保密管理规定》《网络信息内容生态治理规定》《互联网用户账号名称管理规定》等法律条文。',
      '3.用户不得利用易直聘平台创建、传播、复制、发布或扩散任何违反《互联网信息服务管理办法》及其他国家法律法规的资料，也不得以任何方式规避、变相规避或协助他人规避易直聘平台的规则、流程、限制或管理措施。具体如下：',
      '3.1违背宪法所确立的基本原则；',
      '3.2威胁国家安全、泄露国家机密、推翻国家政权、分裂国家统一；',
      '3.3损害国家的荣誉和利益；',
      '3.4煽动民族仇恨、民族歧视，破坏民族团结；',
      '3.5破坏国家宗教政策，宣传邪教和迷信；',
      '3.6散布谣言、扰乱社会秩序、破坏社会稳定；',
      '3.7传播淫秽、色情、赌博、暴力、凶杀、恐怖或诱导犯罪；',
      '3.8侮辱或诽谤他人，侵犯他人合法权益；',
      '3.9含有虚假、有害、威胁、侵犯他人隐私、骚扰、侵犯、诽谤、粗俗、猥亵或违反道德、令人不悦的内容；',
      '3.10其他违约中国法律、法规、规章、条例以及任何具有法律约束力的规范所禁止或限制的内容。',
      '4.易直聘平台严禁用户发布或传播以下信息：包括招聘未成年人等；',
      '4.1涉及传销、直销或广告（寻求合作）等；',
      '4.2涉及色情、淫秽等；',
      '4.3涉及违法/政治敏感等；',
      '4.4涉及虚假信息，如不真实的公司信息、薪资、法人、个人简历、职位信息等；',
      '4.5涉及虚假职位，如发布的职位信息与实际招聘职位不一致等；',
      '4.6其他违反易直聘《招聘行为管理规范》的信息。',
      '5.禁止利用易直聘平台进行损害其他用户或易直聘合法权益的行为，包括但不限于：',
      '5.1假冒任何人或机构，如假冒易直聘工作人员或以虚假或不实的方式陈述或谎称与任何人或机构有关系的；',
      '5.2跟踪或以其他方式骚扰其他用户的；',
      '5.3未经合法授权截获、篡改、收集、储存或删除他人信息、电子邮件或数据资料，或将已获知的此类资料用于任何非法或不正当目的的；',
      '5.4以任何方式干扰或企图干扰易直聘平台的正常运行，或者制作、发布、传播上述干扰易直聘的工具或方法；',
      '5.5未能按照本平台的流程、规则进行注册、认证或使用服务，违反本平台的功能限制或运营策略，或采取任何；',
      '5.6措施规避前述流程、规则、限制或策略的；',
      '5.7未经易直聘公司许可，使用插件、外挂或通过其他第三方工具、运营平台或任何服务接入本服务或系统；',
      '5.8获取相关数据或信息的；',
      '5.9其他违反易直聘《招聘行为管理规范》的行为。',
      '6.用户不得进行损害易直聘平台生态的活动，具体包括：',
      '6.1招募他人参与违法行为；',
      '6.2以培训费、服装费等为由骗取求职者资金；',
      '6.3对其他用户进行恶意骚扰；',
      '6.4利用平台漏洞恶意充值E币（易直聘app中提供的数字化商品，以下统一简称“E币”）、获取虚拟道具等；',
      '6.5在易直聘平台外的第三方平台（如淘宝、闲鱼等）销售E币、虚拟道具等；',
      '6.6通过第三方平台或渠道（例如淘宝店铺）购买E币、虚拟道具等；',
      '6.7用户使用虚假信息注册或认证、发布虚假招聘或求职信息、含有传销、色情、反动等违法内容、泄露面试聊天记录、拖欠农民工工资等，均视为严重违反协议；',
      '6.8违反易直聘《招聘行为管理规范》的其他行为。',
      '7.用户不得利用易直聘平台进行其他违法行为，具体包括：',
      '7.1利用易直聘提供的聊天服务从事欺诈、传销、刷流量、好评、违法物品营销等；',
      '7.2盗用、混淆他人账号信息发布招聘信息或冒充他人名义进行招聘；',
      '7.3可能拖欠或未依法支付工资或劳务报酬，损害劳动者或劳务人员权益的行为；',
      '7.4未经易直聘公司许可，自行或授权、允许、协助任何第三方非法获取平台内信息；“非法获取”指使用“蜘蛛”(spider)程序、爬虫程序、拟人程序等非正常浏览手段、方式，读取、复制、转存、获取数据和信息内容的行为；',
      '7.5为他人提供自动登录平台、代办或协助他人代办身份认证服务或代售身份认证相关材料或凭证；',
      '7.6违反易直聘《招聘行为管理规范》的其他行为。',
      '7.7用户不得实施任何可能干扰平台正常推荐算法运行的行为，包括但不限于虚假点击、恶意刷量、诱导分享等流量作弊行为。',
      '用户一旦违反上述任何规定，即构成违约，易直聘有权根据协议第八条采取相应措施，并追究违约责任。',
    ],
  },
  {
    title: '八、违约责任',
    paragraphs: [
      '1. 若发现用户违反了第七条协议，易直聘有权采取删除、屏蔽信息、限制功能等措施。用户可在收到平台违规通知后3个工作日内提交申诉材料。为防止损失扩大，易直聘在涉嫌刑事犯罪或可能造成人身安全威胁等紧急情况下，可在未通知用户的前提下暂停或终止服务。用户违约导致易直聘受损，易直聘有权要求用户赔偿损失。',
      '2.若用户违约导致第三方索赔或请求，用户需赔偿易直聘或合作伙伴的损失，包括但不限于赔偿金、律师费、调查费等。',
      '3.用户若投诉他人违法或违反协议，应负责不实投诉的法律责任。若侵犯他人权益，投诉人应承担全部责任。投诉方应提供初步证据材料并签署真实性承诺书。若给易直聘带来损失，投诉人应赔偿易直聘。',
    ],
  },
  {
    title: '九、关于用户在易直聘上传或张贴的内容',
    paragraphs: [
      '1. 用户在易直聘平台上传或发布的资料（包括但不限于图片、文字、面试体验和评价等），即视为用户授权易直聘及其关联企业免费、非排他性地使用，范围包括但不限于算法训练、产品优化、反欺诈模型构建等平台运营相关用途。易直聘有权为展示、传播和推广之目的，对这些资料进行复制、调整、编辑、发布、改编、汇编等处理，无需另行通知或支付任何费用。该授权自用户书面告知易直聘停止使用，且易直聘实际收到该通知时终止。',
      '2. 若用户上传或发布的资料侵犯了他人权益，导致任何第三方对易直聘提出侵权或索赔，用户需',
      '全权负责。',
      '3. 对于用户在易直聘公开区域发布的资料，第三方进行复制、调整、编辑、传播等行为，由此产生的法律后果和责任由行为人自行承担，易直聘不承担责任。',
    ],
  },
  {
    title: '十、关于面试聊天等即时通讯服务',
    paragraphs: [
      '1. 根据法律法规要求，易直聘将在商务场合（包括意向咨询、视频面试、直播招聘等），对用户间的交流信息进行临时保存。该等信息将用于支持30天内的聊天历史查询、处理投诉和举报，以及实施安全风险控制策略等目的。',
      '2. 易直聘将确保对这些信息的收集、传输和保存过程采取加密和防泄露等安全措施。',
      '3. 为维护用户隐私，禁止用户下载、传播或公开发布他人的通讯信息，例如面试时的聊天记录。若因此导致易直聘遭受损失或侵犯他人权益，用户需承担相应的违约或赔偿责任。',
    ],
  },
  {
    title: '十一、注销服务',
    paragraphs: [
      '用户可随时要求取消易直聘账户。注销账户的具体规定和步骤，请查阅《注销协议》。一旦用户账户被成功注销，易直聘将依照法律要求，迅速对您的个人信息进行删除或匿名化处理，但为履行法律法规规定的义务、处理争议、维护平台合法权益，易直聘有权在法律允许的范围内留存必要的用户信息。',
    ],
  },
  {
    title: '十二、本协议条款的变更和修改',
    paragraphs: [
      '易直聘有权利，随时依法对本协议的条款进行更改和修订。条款更改后，我们会通过软件更新、系统消息、弹窗或电子邮件等方式，向用户发送最新的《用户协议》，用户应及时查阅。。用户应当定期登录账号关注协议更新，连续30日未登录将被视为已知悉最新协议版本。若用户不同意变更内容，应主动停止使用易直聘服务或申请注销账号。注销账号后，所有未使用的付费权益、增值服务、虚拟商品、积分等权益自动失效，易直聘不承担任何补偿或退款责任，除非法律另有规定。用户继续使用账号的，则视为同意本协议的最新条款。',
    ],
  },
  {
    title: '十三、不可抗力',
    paragraphs: [
      '1. “不可抗力”指的是易直聘无法合理掌控、无法预见，或者即便预见也无法避免的事件，这些事件会妨碍、影响或延迟易直聘根据本注册条款执行其全部或部分职责。这类事件包括但不限于政府行为、自然灾害、战争、黑客攻击、电脑病毒、网络故障等。不可抗力可能导致易直聘无法访问、访问速度缓慢、存储数据丢失、用户个人信息泄露等不良后果。',
      '2. 当遇到不可抗力事件时，易直聘可以暂停履行本协议下的义务，直到不可抗力的影响消除，并且不会因此承担违约责任；但应尽其所能克服该事件，减少其负面影响。',
    ],
  },
  {
    title: '十四、通知',
    paragraphs: [
      '易直聘将向用户发送通知，可能通过系统消息、弹窗、电子邮件或页面公告等途径。当《用户协议》条款有所更改或有其他事项需要变更时，易直聘会利用上述方式告知用户。',
      '通过系统消息、弹窗方式发送的通知，自发送之时起视为已送达用户；通过电子邮件方式发送的，自邮件进入对方邮件系统时视为送达。对于因用户未及时查收等个人原因导致的知情迟延，易直聘不承担责任。',
    ],
  },
  {
    title: '十五、法律的适用和管辖',
    paragraphs: [
      '本合同的生效、执行、阐释以及争议的处理均须遵循中华人民共和国现行的法律法规，合同签署于线上。若您与易直聘之间出现任何争议，双方应首先尝试通过友好协商来解决；若协商失败，您同意将争议提交至易直聘所在地的人民法院进行诉讼解决。即便本合同的某些条款因与中华人民共和国现行法律相冲突而被认定为无效，这也不会影响其他条款的有效性。',
    ],
  },
])

const scrollToSection = (index) => {
  const sectionId = `section-${index}`
  const query = uni.createSelectorQuery().in(pagingRef.value)
  query
    .select(`#${sectionId}`)
    .boundingClientRect((targetRect) => {
      if (targetRect && !Array.isArray(targetRect)) {
        const navQuery = uni.createSelectorQuery()
        navQuery
          .select('.customNavbar')
          .boundingClientRect((navBarRect) => {
            let offsetTop = 150
            if (navBarRect && !Array.isArray(navBarRect)) {
              offsetTop = navBarRect.height
            }
            const targetScrollTop = targetRect.top - offsetTop
            if (pagingRef.value) {
              try {
                pagingRef.value.scrollToY(Math.max(0, targetScrollTop), false)
              } catch (error) {
                uni.pageScrollTo({
                  scrollTop: Math.max(0, targetScrollTop),
                  duration: 0,
                  success: () => {},
                })
              }
            }
          })
          .exec()
      }
    })
    .exec()
}
</script>

<style lang="scss" scoped>
.agreement-container {
  padding: 20rpx 40rpx;
}

.header {
  margin-bottom: 30rpx;
  text-align: center;
}

.title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.subtitle {
  display: block;
  font-size: 24rpx;
  color: #666666;
}

.content-list {
  padding: 20rpx;
  margin-bottom: 30rpx;
  border-radius: 10rpx;
}

.list-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.list-item {
  display: flex;
  align-items: flex-start;
  padding: 10rpx;
  margin-bottom: 10rpx;
  cursor: pointer;
  border-radius: 8rpx;
  .list-text {
    flex: 1;
    font-size: 26rpx;
    color: #007aff;
  }
}

.content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
}

.section {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
}

.paragraph {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333333;
  text-align: justify;
}

.footer {
  margin-top: 40rpx;
  text-align: right;
}

.footer-text {
  display: block;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #666666;
}
</style>
