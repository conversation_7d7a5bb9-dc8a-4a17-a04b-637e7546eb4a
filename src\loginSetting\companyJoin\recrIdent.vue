<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="accountLogin bg-img">
    <CustomNavBar>
      <template #right>
        <wd-img :src="identitySwitchingImg" width="45rpx" height="45rpx" @click="changeIdentFun" />
      </template>
    </CustomNavBar>
    <view class="containner-page">
      <view class="containner-title">身份信息</view>
      <view class="containner-from flex-c m-b-40rpx m-t-40rpx">
        <view class="containner-from-name m-r-10rpx text-26rpx font-w-500">真实姓名：</view>
        <wd-input
          no-border
          type="text"
          v-model="fromData.userName"
          placeholder="真实姓名"
          class="flex-1"
        />
      </view>

      <view class="containner-from flex-c">
        <view class="containner-from-name m-r-10rpx text-26rpx font-w-500">身份证号：</view>
        <wd-input
          no-border
          type="text"
          v-model="fromData.identifyNum"
          placeholder="身份证号码"
          class="flex-1"
        />
      </view>
      <view class="containner-from flex-c m-b-40rpx m-t-40rpx">
        <view class="containner-from-name m-r-10rpx text-26rpx font-w-500">在职岗位：</view>
        <wd-input
          no-border
          type="text"
          :maxlength="10"
          v-model="fromData.hrPosition"
          placeholder="在职岗位"
          class="flex-1"
        />
      </view>

      <view class="login-xy flex-c">
        <!-- <wd-checkbox
          shape="square"
          placeholderClass="placeholderClass"
          size="30"
          v-model="checked"
          checked-color="#44BCC6"
        ></wd-checkbox> -->

        <!-- <view class="login-xy-text">我已阅读并同意《用户协议》《隐私协议》</view> -->
      </view>
      <view class="btn_fixed" @click="submit">
        <view class="btn_box">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { idCardVerify } from '@/interPost/company'
import { idCardNum } from '@/utils/rule'
import identitySwitchingImg from '@/static/mine/business/identity-switching.png'
const { changeIdent } = useChangeIdent()
const fromData = ref({
  userName: '',
  identifyNum: '',
  companyId: null,
  id: null,
  fileId: null,
  hrPosition: '',
})
// 复选框
const checked = ref(false)
// 切换身份
const changeIdentFun = async () => {
  changeIdent()
}
// 完成
const submit = async () => {
  // if (!checked.value) {
  //   uni.showToast({
  //     title: '请先勾选用户协议和隐私协议',
  //     icon: 'none',
  //   })
  //   return
  // }

  if (!fromData.value.userName) {
    uni.showToast({
      title: '请输入真实姓名',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!fromData.value.hrPosition) {
    uni.showToast({
      title: '请输入所在职位',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!idCardNum.test(fromData.value.identifyNum)) {
    uni.showToast({
      title: '请输入正确的身份证号码',
      icon: 'none',
      duration: 3000,
    })
    return
  }

  // console.log(fromData.value, 'fromData.value==')
  const res: any = await idCardVerify(fromData.value)
  if (res.code === 0) {
    uni.reLaunch({
      url: '/loginSetting/companyJoin/jobCertificate?source=recrIdent',
    })
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
</script>

<style lang="scss" scoped>
.containner-page {
  padding: 40rpx;
  .containner-title {
    margin-bottom: 80rpx;
    font-size: 60rpx;
    font-weight: 600;
  }
  .containner-from {
    padding: 30rpx 30rpx;
    margin-right: 20rpx;
    margin-left: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
    box-shadow: 0 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
  }
  .login-xy {
    padding-top: 40rpx;
    margin-left: 20rpx;
    font-weight: 400;

    .login-xy-text {
      font-size: 28rpx;
      color: #000;
    }
  }
  .btn_fixed {
    position: fixed;
    right: 60rpx;
    bottom: 100rpx;
    left: 60rpx;
    box-sizing: border-box;
    width: calc(100% - 120rpx);
    .btn_box {
      .btn_bg {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 30rpx 0rpx;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
        border-radius: 14px 14px 14px 14px;
      }
    }
  }
}
</style>
