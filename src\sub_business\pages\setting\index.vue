<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="设置"></CustomNavBar>
    </template>
    <view class="setting">
      <!-- <view class="setting-list flex-between border-b" @click="goPersonalData">
        <view class="text-32rpx">编辑个人资料</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view> -->
      <view class="setting-list flex-between" @click="goMessageSettings">
        <view class="list-item-text text-32rpx">消息设置</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <!-- <view class="setting-list flex-between" @click="goBlacklist">
        <view class="list-item-text text-32rpx">黑名单</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view> -->
      <view class="setting-list flex-between" @click="goAccountSecurity">
        <view class="list-item-text text-32rpx">账号信息</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goPermissionManage">
        <view class="list-item-text text-32rpx">权限管理</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="divider"></view>
      <view class="setting-list flex-between" @click="goGeneralSetup">
        <view class="list-item-text text-32rpx">清理缓存</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goLegalAgreement">
        <view class="list-item-text text-32rpx">法律协议</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goInfoCollection">
        <view class="list-item-text text-32rpx">信息收集清单</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="divider"></view>
      <view class="setting-list flex-between" @click="goAboutUs">
        <view class="list-item-text text-32rpx">关于易直聘</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="divider"></view>
      <view class="setting-list flex-between">
        <view class="list-item-text text-32rpx">版本更新</view>
        <view class="subText flex-c">
          <view class="p-r-10rpxrpx" v-if="versionInfo?.versionName">
            V{{ versionInfo.versionName }}
          </view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="setting-list flex-between" @click="chanangeIdentity">
        <view class="list-item-text text-32rpx">切换身份</view>
        <view class="subText flex-c">
          <view class="p-r-10rpxrpx">{{ userRoleIsBusiness ? '伯乐' : '黑马' }}</view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="btn-flexd">
        <view class="btn-flexd-red" @click="logoutBtm">退出登录</view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { logout } from '@/interPost/login'
import { clearStorageSync } from '@/utils/storage'
import { useLoginStore } from '@/store'
const loginStore = useLoginStore()
const { userRoleIsBusiness, clearUserInfo } = useUserInfo()
const { stopKeepAlive } = useKeepAlive()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const { getAppVersion } = usePhoneVersion()
const versionInfo = ref<{ versionName?: string; versionCode?: string }>({})
onLoad(async () => {
  versionInfo.value = await getAppVersion()
})
// 编辑个人资料
const goPersonalData = () => {
  uni.navigateTo({
    url: '/sub_business/pages/myInfo/index',
  })
}

// 消息设置
const goMessageSettings = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/message/MessageSettings',
  })
}

// 黑名单
const goBlacklist = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/blacklist/index',
  })
}

// 账号安全
const goAccountSecurity = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/accountInfo/index',
  })
}

// 权限管理
const goPermissionManage = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/permissionManage/index',
  })
}

// 清理缓存
const goGeneralSetup = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/clearCache/index',
  })
}

// 法律协议
const goLegalAgreement = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/PrivacyAgreement',
  })
}

// 个人信息收集清单
const goInfoCollection = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/InfoCollection/index',
  })
}

// 关于我们
const goAboutUs = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/aboutUs/index',
  })
}

// 退出登录
// vuex数据

const logoutBtm = async () => {
  const res: any = await logout()
  console.log(res, '--------')

  // 先停止保活机制，避免后台请求干扰
  stopKeepAlive()
  // 清理登录相关数据
  loginStore.sethomeJobAvtive(0)
  loginStore.sethomeCity1({})
  loginStore.sethomeCity2({})
  loginStore.sethomeCity3({})
  loginStore.setmyjobList([])

  // 清理存储数据
  clearStorageSync()

  // 最后清理用户信息和IM相关操作
  clearUserInfo()

  // 直接跳转到登录页面，避免重复跳转
  uni.reLaunch({
    url: '/pages/login/index',
    success: () => {
      console.log('退出登录成功，已跳转到登录页面')
    },
    fail: (error) => {
      console.error('跳转失败:', error)
      // 如果跳转失败，再次尝试
      setTimeout(() => {
        uni.reLaunch({ url: '/pages/login/index' })
      }, 100)
    },
  })
}
// 切换身份
const chanangeIdentity = () => {
  uni.navigateTo({
    url: '/setting/IdentitySwitching/index',
  })
}
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  // margin-bottom: 320rpx;
  .setting-list {
    padding: 20rpx 20rpx;
    margin: 16rpx 0;
    .list-item-text {
      color: #333;
    }
    // 移除border-b相关样式
  }
}
.divider {
  height: 1rpx;
  margin: 0 20rpx;
  background: #d7d6d6;
}
.btn-flexd-red {
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  text-align: center;
  background-color: #ff8080;
  border-radius: 30rpx;
}

.btn-flexd {
  position: fixed;
  bottom: 40rpx;
  left: 10%;
  width: 80%;
}
</style>
