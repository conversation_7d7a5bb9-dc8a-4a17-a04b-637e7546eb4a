<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '发票记录',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging ref="pagingRef" v-model="pageData" @query="queryList" :paging-style="pageStyle">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          custom-class="px-25rpx"
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">发票记录</text>
          </template>
        </wd-navbar>
      </wd-config-provider>

      <!-- 使用wotUI的tabs组件 -->
      <view class="tabs-wrapper">
        <wd-tabs
          v-model="activeTab"
          @change="handleTabChange"
          :line-width="40"
          line-height="2"
          color="#000000"
          inactive-color="#555555"
          custom-class="custom-tabs"
          slidable="always"
        >
          <wd-tab title="全部" name="all"></wd-tab>
          <wd-tab title="近三月" name="recharge"></wd-tab>
          <wd-tab title="近一年" name="usage"></wd-tab>
        </wd-tabs>
      </view>
    </template>

    <view class="recharge-details-box">
      <!-- 充值明细列表 -->
      <view class="detail-item" v-for="(item, index) in pageData" :key="index">
        <view class="item-row">
          <view class="item-left">
            <view class="item-title">发票记录</view>
            <view class="item-time">{{ item.createTime }}</view>
          </view>
          <view class="item-right">
            <text class="item-amount">{{ item.invoiceMoney }}</text>
            <text class="item-unit">元</text>
          </view>
        </view>
      </view>
    </view>
  </z-paging>

  <wd-toast />
  <wd-message-box />
</template>

<script lang="ts" setup>
import { type ConfigProviderThemeVars } from 'wot-design-uni'
import { usePayProp } from '@/sub_business/hooks/usePayProp'
import { getInvoiceRecordList } from '@/service/walletInvoice'

const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const { clearPayPropActive } = usePayProp()
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
}

const activeTab = ref('all')
const listInfo = ref([])

function handleTabChange(name: string) {
  activeTab.value = name
  pagingRef.value?.reload()
}

const params = ref({
  entity: {},
  page: 1,
  size: 10,
})

const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res: any = await getInvoiceRecordList({
    ...params.value,
    page: pageInfo.page,
    size: pageInfo.size,
  })
  if (res.code === 0) {
    listInfo.value = res.data.list.map((item) => {
      if (item.invoiceMoney) {
        item.invoiceMoney = item.invoiceMoney / 100
      }
      return item
    })
  }

  pagingRef.value.complete(listInfo.value)
}

function handleClickLeft() {
  uni.navigateBack()
}

onLoad(() => {
  pagingRef.value?.reload()
})
onMounted(() => {
  pagingRef.value?.reload()
})

onBeforeUnmount(() => {
  clearPayPropActive()
})
</script>

<style lang="scss" scoped>
.header-tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 60rpx 0;
  background: transparent;
}

.header-title {
  font-size: 48rpx;
  font-weight: 500;
  color: #000000;
}

.header-action {
  padding-left: 40rpx;
  font-size: 28rpx;
  color: #000000;
  cursor: pointer;
}

.tabs-wrapper {
  height: 148rpx;
  padding: 30rpx 60rpx;
  margin-top: 54rpx;
  font-size: 32rpx;
  background: #ffffff;
  border-bottom: 2rpx solid #d7d7d7;
  border-radius: 60rpx 60rpx 0 0;
}
:deep(.wd-tabs__container) {
  height: 0;
}

:deep(.custom-tabs) {
  .wd-tab {
    padding: 32rpx 0;
    font-size: 28rpx;
  }
}
:deep(.wd-tabs__nav-item) {
  font-size: 32rpx !important;
  transition: font-size 0.3s ease;
}

:deep(.wd-tabs__nav-item.is-active) {
  font-size: 32rpx !important;
}

.recharge-details-box {
  padding: 0 60rpx 96rpx;
  background: #ffffff;
}
:deep(.zp-empty-view-center) {
  background: #ffffff !important;
}

:deep(.zp-paging-container) {
  background: #ffffff !important;
}

.item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
  background: #fff;
  border-bottom: 1rpx solid #ededed;
}

.item-left {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.item-time {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #888;
}

.item-right {
  display: flex;
  align-items: baseline;
}

.item-amount {
  font-size: 40rpx;
  font-weight: bold;
  color: #000;
}

.item-unit {
  margin-left: 2rpx;
  font-size: 40rpx;
  font-weight: bold;
  color: #000;
}

.bottom-section {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.total-info {
  display: flex;
  flex: 1;
  align-items: baseline;
}

.total-label {
  font-size: 28rpx;
  color: #333333;
}

.total-amount {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

.total-count {
  margin-right: 20rpx;
  font-size: 24rpx;
  color: #999999;
}

.next-button {
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  color: #ffffff;
  text-align: center;
  background-color: #007aff;
  border-radius: 8rpx;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #888888;
}
</style>
