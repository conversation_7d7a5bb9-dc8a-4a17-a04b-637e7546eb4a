<template>
  <view class="flex justify-center mt-60rpx px-66rpx">
    <view class="w-full flex flex-col items-center relative">
      <view
        class="w-182rpx h-182rpx bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-full center relative z-10"
      >
        <wd-img width="158rpx" height="158rpx" round :src="releaseLogo" />
      </view>
      <view
        class="bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx w-full mt--110rpx p-[126rpx_60rpx_58rpx]"
      >
        <view
          class="grid grid-cols-3 gap-10rpx pb-22rpx border-b-1px border-b-dashed border-b-[#BFBFBF]"
        >
          <view
            v-for="(item, key) in jobTypeOptions"
            :key="`job-${key}`"
            class="h-60rpx text-28rpx font-400 center cursor-pointer"
            :class="
              item.value === releasePostModel.jobType
                ? 'bg-[#D7DFFF] rounded-6rpx border border-solid border-[#649AFF] text-[#649AFF]'
                : 'rounded-10rpx border border-solid border-[#9C9C9C] text-[#333333]'
            "
            @click="handleSelectJobType(item.value as number)"
          >
            {{ item.text }}
          </view>
        </view>
        <wd-config-provider :themeVars="themeVars">
          <wd-form ref="formRef" :model="releasePostModel" error-type="toast">
            <wd-cell
              v-for="(item, key) in filteredFormItemList"
              :key="`form-item-${key}`"
              :prop="item.prop"
              :rules="item.rules"
              vertical
              clickable
              custom-class="pt-28rpx pb-20rpx border-b-1px border-b-dashed border-b-[#BFBFBF]"
              @click="handleFormItemAction(item)"
            >
              <template #title>
                <text class="c-#333333 text-24rpx">{{ item.label }}</text>
              </template>
              <wd-input
                v-if="
                  item.type === 'input' &&
                  !['positionKeyList', 'positionBenefitList'].includes(item.prop)
                "
                v-model="releasePostModel[item.prop as string]"
                :placeholder="item.placeholder"
                suffixIcon="arrow-right"
                no-border
                readonly
              />
              <wd-input
                v-else-if="item.type === 'input' && item.prop === 'positionKeyList'"
                v-model="positionKeyListFormatted"
                :placeholder="item.placeholder"
                suffixIcon="arrow-right"
                no-border
                readonly
              />
              <wd-input
                v-else-if="item.type === 'input' && item.prop === 'positionBenefitList'"
                v-model="positionBenefitListFormatted"
                :placeholder="item.placeholder"
                suffixIcon="arrow-right"
                no-border
                readonly
              />
              <wd-picker
                v-model="releasePostModel[item.prop as any]"
                v-else-if="item.type === 'picker' && item.prop === 'workSalary'"
                :z-index="Number(9999)"
                :placeholder="item.placeholder"
                :columns="item.columns"
                :column-change="onChangeSalaryColumn"
                :display-format="salaryDisplayFormat"
              />
              <wd-picker
                v-model="releasePostModel[item.prop as any]"
                v-else-if="item.type === 'picker'"
                :placeholder="item.placeholder"
                :columns="item.columns"
              />
            </wd-cell>
          </wd-form>
        </wd-config-provider>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { DICT_IDS } from '@/enum'
import { useLoginStore } from '@/store'
import { useReleasePost } from '@/sub_business/hooks/useReleasePost'
import { ReleaseStep } from '@/sub_business/types/release'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import type { FormItemRule } from 'wot-design-uni/components/wd-form/types'
import type {
  ColumnItem,
  PickerViewColumnChange,
} from 'wot-design-uni/components/wd-picker-view/types'
import type { PickerDisplayFormat } from 'wot-design-uni/components/wd-picker/types'
import type { hrPositionAddDataInt } from '@/service/hrPosition/types'
import type { DictOption } from '@/hooks/common/useDictionary'
import releaseLogo from '@/sub_business/static/release/logo.png'

interface FormItem {
  prop: keyof hrPositionAddDataInt
  label: string
  type: 'input' | 'picker' | 'select'
  placeholder: string
  rules: FormItemRule[]
  columns?: (string | number | ColumnItem | (string | number | ColumnItem)[])[]
  action?: () => void
}
const emits = defineEmits<{
  (e: 'nextStep', value: ReleaseStep): void
}>()

const loginStore = useLoginStore()
const { releasePostModel, releasePositionKey, releasePositionMark, releaseSalaryColumns } =
  useReleasePost()
const { getDictOptions } = useDictionary()
const { formRef } = useForm()

const themeVars: ConfigProviderThemeVars = {
  cellPadding: '0',
  cellWrapperPadding: '0',
  cellVerticalTop: '4rpx',
  cellTitleFs: '26rpx',
  cellTapBg: 'transparent',
  inputPlaceholderColor: '#888888',
}
const jobTypeOptions = ref<DictOption[]>([])

const formItemList = reactive<FormItem[]>([
  {
    prop: 'positionName',
    label: '岗位名称',
    type: 'input',
    placeholder: '请选择岗位，如"市场专员"',
    rules: [{ required: true, message: '请填写岗位名称' }],
    action: () => {
      const positionData = releasePostModel.value.positionCode
        ? {
            expectedPositions: releasePostModel.value.positionName,
            expectedPositionsCode: releasePostModel.value.positionCode,
          }
        : {}
      loginStore.setpositionData(positionData)
      uni.navigateTo({
        url: '/loginSetting/category/career',
      })
    },
  },
  {
    prop: 'positionMarkName',
    label: '岗位标签',
    type: 'picker',
    placeholder: '请选择岗位标签',
    rules: [{ required: false, message: '请选择岗位标签' }],
    columns: [],
  },
  {
    prop: 'workExperience',
    label: '工作经验',
    type: 'picker',
    placeholder: '请选择工作经验',
    rules: [{ required: true, message: '请选择工作经验' }],
    columns: [],
  },
  {
    prop: 'workEducational',
    label: '学历要求',
    type: 'picker',
    placeholder: '请选择学历要求',
    rules: [{ required: true, message: '请选择学历要求' }],
    columns: [],
  },
  {
    prop: 'workSalary',
    label: '薪资范围',
    type: 'picker',
    placeholder: '请选择合理的薪资范围',
    rules: [
      {
        required: true,
        message: '请选择合理的薪资范围',
        validator: (value) => {
          if (CommonUtil.isArray(value) && value.length) {
            return Promise.resolve()
          } else {
            return Promise.reject(new Error('请选择薪资范围'))
          }
        },
      },
    ],
    columns: [],
  },
  {
    prop: 'positionKeyList',
    label: '岗位关键词',
    type: 'input',
    placeholder: '被选中的关键词将突出展示给应聘者',
    rules: [
      {
        required: true,
        message: '请至少选择一个岗位关键词',
        validator: (value) => {
          if (CommonUtil.isArray(value) && value.length) {
            return Promise.resolve()
          } else {
            return Promise.reject(new Error('请至少选择一个岗位关键词'))
          }
        },
      },
    ],
    action: () => {
      if (!releasePostModel.value.positionCode) {
        uni.showToast({
          title: '请先选择岗位名称',
          icon: 'none',
        })
        return
      }
      emits('nextStep', ReleaseStep.KEYWORDS)
    },
  },
  {
    prop: 'positionBenefitList',
    label: '岗位待遇',
    type: 'input',
    placeholder: '添加待遇标签将突出展示给应聘者',
    rules: [
      {
        required: true,
        message: '添加待遇标签将突出展示给应聘者',
        validator: (value) => {
          if (CommonUtil.isArray(value) && value.length) {
            return Promise.resolve()
          } else {
            return Promise.reject(new Error('请至少选择一个岗位待遇'))
          }
        },
      },
    ],
    action: () => {
      emits('nextStep', ReleaseStep.SALARY)
    },
  },
])
const filteredFormItemList = computed(() => {
  return formItemList.filter((item) => {
    if (item.prop === 'positionMarkName') {
      return item.columns && item.columns.length > 0
    }
    return true
  })
})
const workExperienceItem = computed(() =>
  formItemList.find((item) => item.prop === 'workExperience'),
)
const workSalaryItem = computed(() => formItemList.find((item) => item.prop === 'workSalary'))
const salaryColumnsFormatted = computed(() => {
  const result: Record<string, ColumnItem[]> = { '0': [] }
  Object.entries(releaseSalaryColumns).forEach(([startSalary, endSalaries]) => {
    result['0'].push({
      label: startSalary,
      value: startSalary,
    })
    if (Array.isArray(endSalaries)) {
      result[startSalary] = endSalaries.map((endSalary) => ({
        label: endSalary,
        value: endSalary,
      }))
    }
  })
  return result
})
const positionKeyListFormatted = computed({
  get: () => {
    const value = releasePostModel.value.positionKeyList
    return value?.join(',') || ''
  },
  set: (val: string) => {
    releasePostModel.value.positionKeyList = val ? val.split(',') : []
  },
})

const positionBenefitListFormatted = computed({
  get: () => {
    const value = releasePostModel.value.positionBenefitList
    return value?.join(',') || ''
  },
  set: (val: string) => {
    releasePostModel.value.positionBenefitList = val ? val.split(',') : []
  },
})

function handleFormItemAction(item: FormItem) {
  item?.action?.()
}
const handleSelectJobType = (value: number) => {
  releasePostModel.value.jobType = value
}
const fetchJobTypeOptions = async () => {
  jobTypeOptions.value = await getDictOptions(DICT_IDS.WORK_TYPE)
  try {
    releasePostModel.value.jobType = jobTypeOptions.value[0]?.value as number
  } catch (error) {}
}
const onChangeSalaryColumn: PickerViewColumnChange = (pickerView, value, columnIndex, resolve) => {
  const item = value[columnIndex]
  if (columnIndex === 0) {
    pickerView.setColumnData(1, salaryColumnsFormatted.value[item.value])
  }
  resolve()
}
const salaryDisplayFormat: PickerDisplayFormat = (items) => {
  return Array.from(new Set(items.map((item: ColumnItem) => item.label))).join('-')
}
const fetchWorkEducationalOptions = async () => {
  const workEducational = formItemList.find((item) => item.prop === 'workEducational')
  const dictData = await getDictOptions(DICT_IDS.EDUCATION_REQUIREMENT)
  workEducational.columns = dictData.map((item) => ({
    label: item.text as string,
    value: item.value,
  }))
}
const fetchPositionMarkCodeOptions = () => {
  const positionMarkName = formItemList.find((item) => item.prop === 'positionMarkName')
  positionMarkName.columns = releasePositionMark.value.map((item) => item.name)
}
const fetchWorkExperienceOptions = async () => {
  const workExperience = formItemList.find((item) => item.prop === 'workExperience')
  const dictData = await getDictOptions(DICT_IDS.EXPERIENCE_REQUIREMENT)
  workExperience.columns = dictData.map((item) => ({
    label: (item.text as AnyObject).label,
    value: item.value,
    ...(item.text as AnyObject),
  }))
}
const fetchSalaryOptions = () => {
  const workSalary = formItemList.find((item) => item.prop === 'workSalary')
  const [begin, _end] = releasePostModel.value.workSalary
  workSalary.columns = [
    salaryColumnsFormatted.value[0],
    salaryColumnsFormatted.value[begin ?? '1k'],
  ]
}
const releaseValidate = async () => {
  return await formRef.value?.validate()
}
onMounted(() => {
  fetchJobTypeOptions()
  fetchWorkEducationalOptions()
  fetchWorkExperienceOptions()
  fetchSalaryOptions()
  fetchPositionMarkCodeOptions()
})

watch(
  () => loginStore.positionObj,
  (positionObj) => {
    if (!positionObj?.expectedPositionsCode) return
    releasePostModel.value.positionName = positionObj.expectedPositions
    releasePostModel.value.positionCode = positionObj.expectedPositionsCode
    releasePositionKey.value = positionObj.expectedPositionsSearchKey
    releasePositionMark.value = positionObj.fourthLevelPositions || []
    fetchPositionMarkCodeOptions()
  },
  {
    deep: true,
  },
)
watch(
  [() => workExperienceItem.value, () => releasePostModel.value.workExperience],
  ([item, experience]) => {
    const columns = (item.columns as ColumnItem[]) || []
    const selectedOption = columns.find((item) => item.value === experience)
    if (!selectedOption) return
    releasePostModel.value.workExperienceStart = selectedOption.workExperienceStart
    releasePostModel.value.workExperienceEnd = selectedOption.workExperienceEnd
  },
  {
    deep: true,
  },
)
watch([() => releasePostModel.value.positionMarkName], ([name]) => {
  if (!name) return
  releasePostModel.value.positionMarkCode =
    releasePositionMark.value.find((item) => item.name === name)?.code ?? null
})
watch(
  [() => workSalaryItem.value, () => releasePostModel.value.workSalary],
  ([_item, salary]) => {
    if (!salary.length) return
    const [begin, end] = salary
    releasePostModel.value.workSalaryBegin = begin === '面议' ? 0 : parseInt(begin, 10) * 1000
    releasePostModel.value.workSalaryEnd = end === '面议' ? 0 : parseInt(end, 10) * 1000
  },
  {
    deep: true,
  },
)

defineExpose({
  releaseValidate,
})
</script>

<style lang="scss" scoped>
//
</style>
