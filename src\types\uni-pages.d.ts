/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/guide/index" |
       "/pages/deepseek/index" |
       "/pages/home/<USER>" |
       "/pages/login/index" |
       "/pages/mine/index" |
       "/pages/news/index" |
       "/loginSetting/accountLogin/index" |
       "/loginSetting/category/career" |
       "/loginSetting/category/expPosition" |
       "/loginSetting/category/index" |
       "/loginSetting/category/JobIntention" |
       "/loginSetting/category/region" |
       "/loginSetting/companyJoin/companyInfo" |
       "/loginSetting/companyJoin/jobCertificate" |
       "/loginSetting/companyJoin/recrIdent" |
       "/loginSetting/createResume/biographicalOne" |
       "/loginSetting/createResume/biographicalTwo" |
       "/loginSetting/CustomerService/index" |
       "/loginSetting/Externalfiling/index" |
       "/loginSetting/regPage/index" |
       "/loginSetting/verifiCode/index" |
       "/chartPage/chartPage/index" |
       "/chartPage/message/index" |
       "/chartPage/message/infoDetail" |
       "/chartPage/message/messageActiveList" |
       "/chartPage/message/messageSysList" |
       "/resumeRelated/AttachmentResume/GeneratePDF" |
       "/resumeRelated/AttachmentResume/index" |
       "/resumeRelated/AttachmentResume/WebViewpdf" |
       "/resumeRelated/AttachmentResume/WebViewUpload" |
       "/resumeRelated/collect/index" |
       "/resumeRelated/collectResume/index" |
       "/resumeRelated/communicate/index" |
       "/resumeRelated/company/index" |
       "/resumeRelated/component/interviewCard" |
       "/resumeRelated/corporateName/index" |
       "/resumeRelated/CustomerService/index" |
       "/resumeRelated/education/index" |
       "/resumeRelated/education/major" |
       "/resumeRelated/education/school" |
       "/resumeRelated/filter/index" |
       "/resumeRelated/HomeRegion/index" |
       "/resumeRelated/HomeSearch/index" |
       "/resumeRelated/interview/index" |
       "/resumeRelated/interview/WaitMeeting" |
       "/resumeRelated/jobDetail/hrJobDetail" |
       "/resumeRelated/jobDetail/index" |
       "/resumeRelated/jobExpectations/index" |
       "/resumeRelated/moreJobs/index" |
       "/resumeRelated/mySkill/index" |
       "/resumeRelated/onlineResume/index" |
       "/resumeRelated/pdf-upload/pdf-upload" |
       "/resumeRelated/preview/index" |
       "/resumeRelated/projectExperience/index" |
       "/resumeRelated/projectExperience/workContent" |
       "/resumeRelated/projectExperience/workPerformance" |
       "/resumeRelated/recruiter/index" |
       "/resumeRelated/resumeIndustry/index" |
       "/resumeRelated/salaryWork/index" |
       "/resumeRelated/seekEmployment/index" |
       "/resumeRelated/violationDis/index" |
       "/resumeRelated/workExperience/dept" |
       "/resumeRelated/workExperience/index" |
       "/resumeRelated/workExperience/workContent" |
       "/resumeRelated/workExperience/workPerformance" |
       "/resumeRelated/workExperience/workSkills" |
       "/resumeRelated/onlineResume/certificate/index" |
       "/resumeRelated/onlineResume/myAdvantage/index" |
       "/resumeRelated/onlineResume/portfolio/index" |
       "/resumeRelated/onlineResume/qualiCertificate/index" |
       "/setting/aboutUs/index" |
       "/setting/accountMange/index" |
       "/setting/accountNumber/index" |
       "/setting/AdressMange/index" |
       "/setting/certificateImage/businesslicense" |
       "/setting/certificateImage/humanResources" |
       "/setting/certificateImage/TelServices" |
       "/setting/generalSetup/index" |
       "/setting/identityAuth/index" |
       "/setting/IdentitySwitching/index" |
       "/setting/InfoCollection/index" |
       "/setting/loginDevice/index" |
       "/setting/notice/index" |
       "/setting/permission/index" |
       "/setting/personalInfo/index" |
       "/setting/PersonalInfoList/basicInfo" |
       "/setting/PersonalInfoList/IdentityInfo" |
       "/setting/PersonalInfoList/index" |
       "/setting/phoneUpdata/index" |
       "/setting/PrivacyAgreement/CancelAgreement" |
       "/setting/PrivacyAgreement/CommAgreement" |
       "/setting/PrivacyAgreement/EnterpriseResourses" |
       "/setting/PrivacyAgreement/HumanResources" |
       "/setting/PrivacyAgreement/index" |
       "/setting/PrivacyAgreement/PrivacyPolicy" |
       "/setting/PrivacyAgreement/Recruitmentbehavior" |
       "/setting/PrivacyAgreement/ResumeUser" |
       "/setting/PrivacyAgreement/UserAgreement" |
       "/setting/PrivacyAgreement/UserRules" |
       "/setting/setting/index" |
       "/setting/sharingInfoList/index" |
       "/setting/wxUpdata/index" |
       "/paymentRelated/positionPay/index" |
       "/sub_business/pages/AddressCenter/AddressAdd" |
       "/sub_business/pages/AddressCenter/index" |
       "/sub_business/pages/communicate/index" |
       "/sub_business/pages/company/index" |
       "/sub_business/pages/inappropriate/index" |
       "/sub_business/pages/interview/detail" |
       "/sub_business/pages/interview/index" |
       "/sub_business/pages/interview/Initiate" |
       "/sub_business/pages/myInfo/index" |
       "/sub_business/pages/order/index" |
       "/sub_business/pages/positionManage/index" |
       "/sub_business/pages/prop/cdk" |
       "/sub_business/pages/prop/details" |
       "/sub_business/pages/prop/index" |
       "/sub_business/pages/prop/pay" |
       "/sub_business/pages/prop/record" |
       "/sub_business/pages/prop/redetail" |
       "/sub_business/pages/prop/remorelist" |
       "/sub_business/pages/rapid-processing/index" |
       "/sub_business/pages/recruitmentData/index" |
       "/sub_business/pages/release/index" |
       "/sub_business/pages/service/index" |
       "/sub_business/pages/setting/index" |
       "/sub_business/pages/tucked/index" |
       "/sub_business/pages/walletInvoice/index" |
       "/sub_business/pages/company/model/companyBenefit" |
       "/sub_business/pages/company/model/companyProfile" |
       "/sub_business/pages/company/model/companyStyleList" |
       "/sub_business/pages/company/model/shortCompany" |
       "/sub_business/pages/myInfo/model/hrPosition" |
       "/sub_business/pages/prop/coupons/index" |
       "/sub_business/pages/setting/model/MessageSettings" |
       "/sub_business/pages/setting/model/PrivacyAgreement" |
       "/sub_business/pages/walletInvoice/InvoiceApplication/index" |
       "/sub_business/pages/walletInvoice/InvoiceRecord/index" |
       "/sub_business/pages/walletInvoice/Invoicing/index" |
       "/sub_business/pages/walletInvoice/rechargeDetails/index" |
       "/sub_business/pages/setting/model/aboutUs/index" |
       "/sub_business/pages/setting/model/accountInfo/index" |
       "/sub_business/pages/setting/model/accountSecurity/index" |
       "/sub_business/pages/setting/model/accountSecurity/PasswordChange" |
       "/sub_business/pages/setting/model/accountSecurity/PhoneChange" |
       "/sub_business/pages/setting/model/blacklist/index" |
       "/sub_business/pages/setting/model/clearCache/index" |
       "/sub_business/pages/setting/model/deregisterAccount/index" |
       "/sub_business/pages/setting/model/InfoCollection/index" |
       "/sub_business/pages/setting/model/loginDevice/index" |
       "/sub_business/pages/setting/model/message/CommonGreetings" |
       "/sub_business/pages/setting/model/message/CommonPhrases" |
       "/sub_business/pages/setting/model/message/MessageSettings" |
       "/sub_business/pages/setting/model/permissionManage/index" |
       "/sub_business/pages/setting/model/PersonalInfoList/basicInfo" |
       "/sub_business/pages/setting/model/PersonalInfoList/IdentityInfo" |
       "/sub_business/pages/setting/model/PersonalInfoList/index" |
       "/sub_business/pages/setting/model/qualifications/index" |
       "/sub_business/pages/setting/model/Resignation/index" |
       "/sub_common/pages/phrases/add" |
       "/sub_common/pages/phrases/index" |
       "/ChatUIKit/modules/Chat/index" |
       "/ChatUIKit/modules/VideoPreview/index";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/home/<USER>" | "/pages/deepseek/index" | "/pages/news/index" | "/pages/mine/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
