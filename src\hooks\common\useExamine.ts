import { companyApply } from '@/service/common'

export const useExamine = () => {
  const { setExamineStateInit } = useUserInfo()
  const getExamineState = async () => {
    try {
      const { data } = await companyApply({
        custom: {
          catch: true,
        },
      })
      setExamineStateInit(data.status)
    } catch (error) {
      return Promise.reject(error)
    }
  }
  return {
    getExamineState,
  }
}
