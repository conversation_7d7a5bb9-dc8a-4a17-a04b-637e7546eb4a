<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useBaseUrlList } from '@/hooks/common/useBaseUrlList'
// const aLiSDKModule = uni.requireNativePlugin('AliCloud-NirvanaPns')
const { baseUrlMsgListIntApi } = useBaseUrlList()
const { initEaseMobIM } = useEaseMobIM()

onLaunch(async () => {
  console.log('App Launch')
  baseUrlMsgListIntApi()
  initEaseMobIM()
  // #ifdef APP-PLUS
  setTimeout(() => {
    plus.navigator.closeSplashscreen()
  }, 3000)
  // #endif
  // let platform = uni.getSystemInfoSync().platform
  // let sdkInfo =
  //   'wqC1pkVLcX+64LTv+EZFsvR/BjtwHED8TxkIyoXJBbolvjg8qm9ia0weQvkhKPU9yNxNZYB5humF3kN377V2dKN1IoULNulsVutUN+PpfFcsGORDW/qdZnWqEs3alU2HSdG+tGKJjxl7B+vHk5FeHrIoM6ntd0XCGY/gt+rfuEbBtGFWi+vAI8vLk0SsaQGDXTuS5n7SWPwAj+AOe/XhWgjnqsU3HtbxREQNTYfuOvpc/LpC7I2uKxWxNQGUJuBS4aoSTbfIECQAJm8qAaxMNiPaFIsTvy/tWBSHMbtKWbPAnbvw4zuFMxESCDLvyrva'

  // //设置秘钥
  // if (platform == 'android') {
  //   //开启SDK日志打印
  //   aLiSDKModule.setLoggerEnable(true)
  //   aLiSDKModule.expandAuthPageCheckedScope(true)
  //   //禁用物理返回键
  //   //aLiSDKModule.closeAuthPageReturnBack(true);
  //   //开启区分界面返回及物理返回功能，自动控制后续返回事件
  //   aLiSDKModule.userControlAuthPageCancel()
  //   //是否跟随系统深色模式
  //   aLiSDKModule.setAuthPageUseDayLight(false)
  // } else if (platform == 'ios') {
  // }
  // aLiSDKModule.setAuthSDKInfo(sdkInfo)

  console.log('App Launch')
})
onShow(() => {
  console.log('App Show')
})
onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
/* 每个页面公共css */
@import 'style/reset';
@import 'style/common';
@import 'style/iconfont-weapp-icon.css';
// @import 'components/gaoyia-parse/parse.css';
</style>
